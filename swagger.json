{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http"], "swagger": "2.0", "info": {"title": "Sport Guess API", "version": "1.0"}, "host": "127.0.0.1:8888", "basePath": "/", "paths": {"/api/nba/card-group/exchange": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "NBA卡组兑换", "operationId": "nabNbaExchangeCardGroup", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["nab_card_groups_id"], "properties": {"nab_card_groups_id": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/card-group/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "获取NBA卡组列表", "operationId": "nabNbaGetCardGroupList", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/draw-card": {"post": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "NBA抽卡", "operationId": "nabNbaDrawCard", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/guess/my-list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "我的竞猜列表", "operationId": "nabNbaMyGuessList", "parameters": [{"type": "integer", "name": "is_open_result", "in": "query", "required": true}, {"type": "string", "name": "date", "in": "query", "allowEmptyValue": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/guess/set-heavy-hammer": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "设为重锤单", "operationId": "nabNbaSetHeavyHammer", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_guess_record_id"], "properties": {"user_guess_record_id": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/guess/submit": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "提交竞猜", "operationId": "nabNbaSubmitGuess", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["guesses"], "properties": {"guesses": {"type": "array", "items": {"type": "object", "required": ["match_id", "type", "who_win"], "properties": {"match_id": {"type": "integer"}, "type": {"type": "integer"}, "who_win": {"type": "integer"}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/match/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "获取NBA赛程列表", "operationId": "nabNabmatchlist", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "required": ["list", "user_guess_count", "date"], "properties": {"date": {"type": "string"}, "list": {"type": "array", "items": {"type": "object", "required": ["match_id", "match_time", "home", "home_logo", "away", "away_logo", "home_goals", "away_goals", "handicap_status", "close_time", "concede_num", "over_under_num", "handicap_guess", "over_under_guess"], "properties": {"away": {"type": "string"}, "away_goals": {"type": "integer"}, "away_logo": {"type": "string"}, "close_time": {"type": "string"}, "concede_num": {"type": "string"}, "handicap_guess": {"type": "integer"}, "handicap_status": {"type": "integer"}, "home": {"type": "string"}, "home_goals": {"type": "integer"}, "home_logo": {"type": "string"}, "match_id": {"type": "integer"}, "match_time": {"type": "string"}, "over_under_guess": {"type": "integer"}, "over_under_num": {"type": "string"}}}}, "user_guess_count": {"type": "integer"}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/nba/star/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["NBA活动"], "summary": "获取NBA球星列表", "operationId": "nabNbaGetStarList", "parameters": [{"type": "string", "name": "level", "in": "query", "allowEmptyValue": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/guess/my-list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["竞猜"], "summary": "我的竞猜列表", "operationId": "guessMyGuessList", "parameters": [{"type": "string", "name": "comp_short_zh", "in": "query", "allowEmptyValue": true}, {"type": "integer", "name": "round_num", "in": "query", "allowEmptyValue": true}, {"type": "integer", "name": "is_open_result", "in": "query", "required": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/guess/set-heavy-hammer": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["竞猜"], "summary": "设为重锤单", "operationId": "guessSet<PERSON><PERSON><PERSON><PERSON><PERSON>mer", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_guess_record_id"], "properties": {"user_guess_record_id": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/guess/submit": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["竞猜"], "summary": "提交竞猜", "operationId": "guessSubmit<PERSON>uess", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["guesses"], "properties": {"guesses": {"type": "array", "items": {"type": "object", "required": ["match_id", "type", "who_win"], "properties": {"match_id": {"type": "integer"}, "type": {"type": "integer"}, "who_win": {"type": "integer"}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/match/default-round-list": {"post": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "tags": ["赛程"], "summary": "获取各赛事默认轮次", "operationId": "matchDefaultRoundList", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object", "required": ["comp_short_zh", "round_num"], "properties": {"comp_short_zh": {"type": "string"}, "round_num": {"type": "integer"}}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/match/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["赛程"], "summary": "获取赛程列表", "operationId": "matchGetMatchList", "parameters": [{"type": "string", "name": "comp_short_zh", "in": "query", "required": true}, {"type": "integer", "name": "round_num", "in": "query", "required": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "required": ["list", "user_guess_count", "min_guess_count_for_reward", "round_status"], "properties": {"list": {"type": "array", "items": {"type": "object", "required": ["match_id", "match_time", "home", "home_logo", "away", "away_logo", "home_goals", "away_goals", "handicap_status", "close_time", "concede_num", "over_under_num", "handicap_guess", "over_under_guess"], "properties": {"away": {"type": "string"}, "away_goals": {"type": "integer"}, "away_logo": {"type": "string"}, "close_time": {"type": "string"}, "concede_num": {"type": "string"}, "handicap_guess": {"type": "integer"}, "handicap_status": {"type": "integer"}, "home": {"type": "string"}, "home_goals": {"type": "integer"}, "home_logo": {"type": "string"}, "match_id": {"type": "integer"}, "match_time": {"type": "string"}, "over_under_guess": {"type": "integer"}, "over_under_num": {"type": "string"}}}}, "min_guess_count_for_reward": {"type": "integer"}, "round_status": {"type": "integer"}, "user_guess_count": {"type": "integer"}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/ranking/heavy-hammer": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["排行榜"], "summary": "重锤命中率排行榜", "operationId": "rankingHeavyHammerRanking", "parameters": [{"type": "string", "name": "start_time", "in": "query", "allowEmptyValue": true}, {"type": "string", "name": "end_time", "in": "query", "allowEmptyValue": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object", "required": ["rank", "username", "hit_count", "total_count", "hit_rate"], "properties": {"hit_count": {"type": "integer"}, "hit_rate": {"type": "string"}, "rank": {"type": "integer"}, "total_count": {"type": "integer"}, "username": {"type": "string"}}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/ranking/profit": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["排行榜"], "summary": "盈利排行榜", "operationId": "rankingProfitRanking", "parameters": [{"type": "string", "name": "start_time", "in": "query", "allowEmptyValue": true}, {"type": "string", "name": "end_time", "in": "query", "allowEmptyValue": true}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/user/detail": {"post": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "tags": ["用户"], "summary": "用户信息", "operationId": "accountDetail", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "required": ["id", "username", "reward_amount", "nba_reward_amount"], "properties": {"id": {"type": "integer"}, "nba_reward_amount": {"type": "string"}, "reward_amount": {"type": "string"}, "username": {"type": "string"}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}, "/api/v1/user/third-party-login": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["用户"], "summary": "第三方登录", "operationId": "accountThirdpartylogin", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["userId", "name", "platformId"], "properties": {"name": {"type": "string"}, "platformId": {"type": "string"}, "userId": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string"}}}, "error_msg": {"type": "string"}, "message": {"type": "string"}}}}}}}}, "x-date": "2025-08-30 11:13:15", "x-description": "This is a goctl generated swagger file.", "x-github": "https://github.com/zeromicro/go-zero", "x-go-zero-doc": "https://go-zero.dev/", "x-goctl-version": "1.8.5"}