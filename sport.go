package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"sport-server/internal/config"
	"sport-server/internal/constants"
	"sport-server/internal/handler"
	"sport-server/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stat"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/sport-api.yaml", "the config file")

func main() {

	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	// 禁用统计日志
	stat.DisableLog()

	// 禁用go-zero框架自带的SQL日志（使用自定义的SQL日志）
	sqlx.DisableLog()

	// 设置环境变量禁用HTTP访问日志
	os.Setenv("GOZERO_LOG_LEVEL", "error")

	// 创建服务器，禁用访问日志
	restConf := c.RestConf
	restConf.Log.Mode = "" // 禁用框架自带的HTTP日志

	server := rest.MustNewServer(c.RestConf, rest.WithCustomCors(func(header http.Header) {
		header.Set("Access-Control-Allow-Origin", "*")
		header.Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, DELETE")
		header.Set("Access-Control-Allow-Headers", "Content-Type, Authorization, AccessToken, Token, device-info,preflight")
		header.Set("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers")
		header.Set("Access-Control-Allow-Credentials", "true")
		header.Set("Access-Control-Max-Age", "86400")
	}, nil, "*"))
	defer server.Stop()

	// 注释掉请求日志中间件，只保留SQL日志
	// server.Use(middleware.NewRequestLogMiddleware().Handle)

	ctx := svc.NewServiceContext(c)

	server.AddRoute(rest.Route{
		Method: http.MethodOptions,
		Path:   "/api/*",
		Handler: func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		},
	})

	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)

	// 只在dev环境注册swagger路由
	if c.Environment == constants.EnvDev {
		handler.RegisterSwaggerRoutes(server)
		fmt.Printf("Swagger UI available at: http://%s:%d/swagger/index.html\n", c.Host, c.Port)
	}

	server.Start()
}
