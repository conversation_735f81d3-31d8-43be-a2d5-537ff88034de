# Sport Server

基于 go-zero 框架开发的体育竞猜api服务，提供用户竞猜、排行榜等功能。

## 注意：
```bash
internal/handler 
internal/types
```
## 这两个目录里面的新增的接口文件全是使用goctl工具生成的，不要做手工党，不要给自己增加无谓的工作量！！！！！！
## 在.api文件定义后直接生成后只需要写logic业务层的代码！！！切记不要给自己增加无谓的工作量，也不要给别人添加无谓的工作量！

![img.png](img.png)
## 不熟悉go-zero的请注意idea上面会有这行提示，这种文件全是不能手动改的！！！


## 📋 目录结构

```
sport-server/
├── api/                    # API定义文件
│   ├── sport.api          # API接口定义
│   └── sport.md           # API文档
├── cmd/                   # 应用程序入口
│   ├── api/              # API服务
│   │   └── main.go       # API服务入口
│   └── cron/             # 定时任务服务
│       └── main.go       # 定时任务服务入口
├── etc/                   # 配置文件
│   └── settings.yaml      # 服务配置
├── internal/              # 内部代码
│   ├── config/           # 配置结构体
│   ├── constants/        # 常量定义
│   ├── cron/             # 定时任务相关
│   │   ├── jobs/         # 具体任务实现
│   │   └── scheduler/    # 调度器
│   ├── handler/          # HTTP处理器
│   ├── logic/            # 业务逻辑
│   ├── middleware/       # 中间件
│   ├── model/           # 数据模型
│   ├── svc/             # 服务上下文
│   ├── types/           # 类型定义
│   └── utils/           # 工具函数
├── swagger.json         # Swagger文档（自动生成）
└── README.md           # 项目说明
```


## 📦 依赖安装

### 安装 goctl 工具
```bash
go install github.com/zeromicro/go-zero/tools/goctl@latest
```


## 🚀 启动服务

### 1. 配置文件
编辑 `etc/sport-api.yaml` 配置数据库和Redis连接信息：

```yaml
# MySQL配置
DataSource: root:123456@tcp(127.0.0.1:3306)/activity?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

# Redis配置
Redis:
  Host: 127.0.0.1:6379
  Pass: "123456"
```

### 2. 启动服务
```bash
# 开发环境启动API服务
go run sport.go

# 或编译后启动API服务
go build -o sport-server sport.go
./sport-server

# 启动定时任务服务
go run cmd/cron/main.go
# 或编译后启动
go build -o sport-cron cmd/cron/main.go
./sport-cron
```

### 3. 访问服务
- **API服务**: http://0.0.0.0:8888
- **Swagger文档**: http://0.0.0.0:8888/swagger/index.html
- **定时任务服务**: 后台运行，通过日志查看执行状态

## 🔧 开发规范

### 文件命名规范
本项目严格遵循以下命名规范：

#### Handler 和 Logic 文件
- **格式**: 全小写无下划线
- **示例**: 
  - `submitguesshandler.go`

#### Model 文件
- **格式**: 小写加下划线
- **示例**: 
  - `db_user_guess_record.go`

#### Middleware 文件
- **格式**: 全小写无下划线
- **示例**: 
  - `authmiddleware.go`

### API定义规范
在 `api/sport.api` 中定义接口时：

```api
@doc "接口描述"
@handler handlername  // 全小写无下划线
get/post /path (RequestType) returns (ResponseType)
```

## 🛠 开发工具使用

### 1. Swagger文档更新
Swagger文档会根据 `api/sport.api` 自动生成都会检查并更新：
```bash
# 生成命令
goctl api swagger -api api/sport.api -dir . --filename swagger

# 访问Swagger文档
open http://0.0.0.0:8888/swagger/index.html
```

### 2. 使用 goctl 快速开发新接口

#### 步骤1: 在API文件中定义接口
编辑 `api/sport.api`，添加新接口定义：

```api
type NewApiReq {
    Param1 string `json:"param1"`
    Param2 int    `json:"param2"`
}

type NewApiResp {
    BaseResponse
    Data string `json:"data"`
}

service sport-api {
    @doc "新接口描述"
    @handler newapi
    post /new/api (NewApiReq) returns (NewApiResp)
}
```

#### 步骤2: 生成代码
```bash
# 生成handler和logic代码
./generate.sh
```

#### 步骤3: 实现业务逻辑
在生成的logic文件中实现具体的业务逻辑。

