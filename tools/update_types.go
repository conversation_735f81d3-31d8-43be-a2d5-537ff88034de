package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"strings"
)

func main() {
	typesFile := "internal/types/types.go"

	// 检查文件是否存在
	if _, err := os.Stat(typesFile); os.IsNotExist(err) {
		fmt.Printf("错误: %s 文件不存在\n", typesFile)
		os.Exit(1)
	}

	// 解析 Go 文件
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, typesFile, nil, parser.ParseComments)
	if err != nil {
		fmt.Printf("解析文件失败: %v\n", err)
		os.Exit(1)
	}

	// 提取所有响应类型
	var responseTypes []string

	for _, decl := range node.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.TYPE {
			for _, spec := range genDecl.Specs {
				if typeSpec, ok := spec.(*ast.TypeSpec); ok {
					typeName := typeSpec.Name.Name
					// 查找以 "Resp" 结尾的类型
					if strings.HasSuffix(typeName, "Resp") {
						responseTypes = append(responseTypes, typeName)
					}
				}
			}
		}
	}

	if len(responseTypes) == 0 {
		fmt.Println("警告: 没有找到任何响应类型")
		return
	}

	// 生成类型管理函数
	functionCode := generateTypeManagementFunction(responseTypes)

	// 读取原文件内容
	content, err := os.ReadFile(typesFile)
	if err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		os.Exit(1)
	}

	// 检查是否已经存在 GetAllResponseTypes 函数
	contentStr := string(content)
	if strings.Contains(contentStr, "func GetAllResponseTypes()") {
		// 如果已存在，替换整个函数
		lines := strings.Split(contentStr, "\n")
		var newLines []string
		skipFunction := false

		for _, line := range lines {
			if strings.Contains(line, "func GetAllResponseTypes()") {
				skipFunction = true
				continue
			}
			if skipFunction && strings.HasPrefix(line, "}") && !strings.Contains(line, "\t") {
				skipFunction = false
				continue
			}
			if !skipFunction {
				newLines = append(newLines, line)
			}
		}

		contentStr = strings.Join(newLines, "\n")
		// 移除末尾多余的空行
		contentStr = strings.TrimRight(contentStr, "\n") + "\n"
	}

	// 追加新的函数
	finalContent := contentStr + functionCode

	// 写回文件
	err = os.WriteFile(typesFile, []byte(finalContent), 0644)
	if err != nil {
		fmt.Printf("写入文件失败: %v\n", err)
		os.Exit(1)
	}

	//fmt.Printf("成功更新类型列表")
}

func generateTypeManagementFunction(responseTypes []string) string {
	var builder strings.Builder

	builder.WriteString("\n// GetAllResponseTypes 返回所有响应类型的实例\n")
	builder.WriteString("// 此函数由 tools/update_types.go 自动生成，请勿手动修改\n")
	builder.WriteString("func GetAllResponseTypes() []interface{} {\n")
	builder.WriteString("\treturn []interface{}{\n")

	for _, typeName := range responseTypes {
		builder.WriteString(fmt.Sprintf("\t\t%s{},\n", typeName))
	}

	builder.WriteString("\t}\n")
	builder.WriteString("}\n")

	builder.WriteString("\n// GetResponseTypeNames 返回所有响应类型的名称\n")
	builder.WriteString("// 此函数由 tools/update_types.go 自动生成，请勿手动修改\n")
	builder.WriteString("func GetResponseTypeNames() []string {\n")
	builder.WriteString("\treturn []string{\n")

	for _, typeName := range responseTypes {
		builder.WriteString(fmt.Sprintf("\t\t\"%s\",\n", typeName))
	}

	builder.WriteString("\t}\n")
	builder.WriteString("}\n")

	return builder.String()
}
