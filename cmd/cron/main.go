package main

import (
	"database/sql"
	"flag"
	"os"
	"os/signal"
	"syscall"

	"sport-server/internal/config"
	"sport-server/internal/cron/jobs"
	"sport-server/internal/cron/scheduler"
	"sport-server/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"

	_ "github.com/go-sql-driver/mysql"
)

var configFile = flag.String("f", "etc/sport-api.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	// 设置日志
	logx.MustSetup(logx.LogConf{
		ServiceName: "sport-cron",
		Mode:        "console",
		Level:       "info",
	})

	// 检查定时任务是否启用
	if !c.Cron.Enabled {
		logx.Info("定时任务已禁用，程序退出")
		return
	}

	// 初始化数据库连接
	db, err := sql.Open("mysql", c.DataSource)
	if err != nil {
		logx.Errorf("数据库连接失败: %v", err)
		os.Exit(1)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		logx.Errorf("数据库连接测试失败: %v", err)
		os.Exit(1)
	}

	logx.Info("数据库连接成功")

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(c)

	// 创建调度器
	s := scheduler.NewScheduler()

	// 注册任务
	handicapJob := jobs.NewHandicapStatusJob(db)
	if err := s.RegisterJob(handicapJob); err != nil {
		logx.Errorf("注册封盘状态任务失败: %v", err)
		os.Exit(1)
	}

	// 注册兑换抽奖次数任务
	exchangeLotteryJob := jobs.NewExchangeLotteryJob(svcCtx)
	if err := s.RegisterJob(exchangeLotteryJob); err != nil {
		logx.Errorf("注册兑换抽奖次数任务失败: %v", err)
		os.Exit(1)
	}

	// 启动调度器
	s.Start()

	logx.Info("定时任务服务启动成功")

	// 等待退出信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logx.Info("正在关闭定时任务服务...")
	s.Stop()
	logx.Info("定时任务服务已关闭")
}
