package constants

// 环境配置
const (
	EnvDev  = "dev"
	EnvProd = "pro"
)

// 竞猜类型
const (
	GuessTypeHandicap  = 1 // 让球
	GuessTypeOverUnder = 2 // 大小球
)

// 竞猜结果 - 猜谁胜
const (
	WhoWinHome = 1 // 主胜
	WhoWinAway = 2 // 客胜
)

// 有效的竞猜类型map
var ValidGuessTypes = map[int]bool{
	GuessTypeHandicap:  true,
	GuessTypeOverUnder: true,
}

// 有效的猜谁胜map
var ValidWhoWinTypes = map[int]bool{
	WhoWinHome: true,
	WhoWinAway: true,
}

// 竞猜结果
const (
	GuessResultWin  = 1 // 赢
	GuessResultLose = 2 // 输
	GuessResultDraw = 3 // 和局
)

// 盘口状态
const (
	HandicapStatusNotOpen = 1 // 未开盘
	HandicapStatusOpen    = 2 // 开盘中
	HandicapStatusClosed  = 3 // 已截止
	HandicapStatusSettled = 4 // 已结算
)

// 竞猜相关常量
const (
	MinGuessCountForReward = 3 // 至少多少场才能参与胜率返利
)

// 封盘状态
const (
	RoundStatusOpen   = 0 // 未封盘
	RoundStatusClosed = 1 // 已封盘
)

const (
	CanNotHeavyHammer = 0 // 不能设置重锤单
	CanHeavyHammer    = 1 // 能设置重锤单
)

var CompShortZhMaps = []string{
	"英超",
	"德甲",
	"西甲",
	"意甲",
	"法甲",
}

// NBA卡组奖励类型
const (
	RewardTypeMoney  = 1 // 彩金
	RewardTypeCoupon = 2 // 存送券
)

// 兑换状态
const (
	ExchangeStatusNotExchanged = 0 // 未兑换
	ExchangeStatusExchanged    = 1 // 已兑换
)
