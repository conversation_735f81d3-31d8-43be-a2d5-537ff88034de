package utils

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// LoggedSqlConn 包装sqlx.SqlConn以添加SQL日志
type LoggedSqlConn struct {
	sqlx.SqlConn
}

// NewLoggedSqlConn 创建带日志的SQL连接
func NewLoggedSqlConn(conn sqlx.SqlConn) sqlx.SqlConn {
	return &LoggedSqlConn{SqlConn: conn}
}

func (l *LoggedSqlConn) ExecCtx(ctx context.Context, query string, args ...interface{}) (result sql.Result, err error) {
	start := time.Now()
	result, err = l.SqlConn.ExecCtx(ctx, query, args...)
	duration := time.Since(start)

	logSQL("EXEC", query, args, duration, err)
	return
}

func (l *LoggedSqlConn) QueryRowCtx(ctx context.Context, v interface{}, query string, args ...interface{}) error {
	start := time.Now()
	err := l.SqlConn.QueryRowCtx(ctx, v, query, args...)
	duration := time.Since(start)

	logSQL("QUERY_ROW", query, args, duration, err)
	return err
}

func (l *LoggedSqlConn) QueryRowsCtx(ctx context.Context, v interface{}, query string, args ...interface{}) error {
	start := time.Now()
	err := l.SqlConn.QueryRowsCtx(ctx, v, query, args...)
	duration := time.Since(start)

	logSQL("QUERY_ROWS", query, args, duration, err)
	return err
}

func logSQL(operation, query string, args []interface{}, duration time.Duration, err error) {
	status := "SUCCESS"
	if err != nil {
		status = "ERROR"
	}

	// 将占位符替换为实际参数值
	finalQuery := replacePlaceholders(query, args)

	// 始终输出SQL执行日志
	logx.Infof("[SQL %s] %s | Duration: %v | Status: %s",
		operation,
		finalQuery,
		duration,
		status)

	// 只有非"no rows in result set"错误才输出错误日志
	if err != nil && err.Error() != "sql: no rows in result set" {
		logx.Errorf("[SQL ERROR] %v", err)
	}
}

// replacePlaceholders 将SQL中的?占位符替换为实际参数值
func replacePlaceholders(query string, args []interface{}) string {
	// 首先清理SQL格式化字符
	cleanQuery := cleanSQLFormat(query)

	if len(args) == 0 {
		return cleanQuery
	}

	result := cleanQuery
	for _, arg := range args {
		// 将参数转换为字符串格式
		var argStr string
		switch v := arg.(type) {
		case string:
			argStr = fmt.Sprintf("'%s'", v)
		case nil:
			argStr = "NULL"
		case int, int8, int16, int32, int64:
			argStr = fmt.Sprintf("%v", v)
		case uint, uint8, uint16, uint32, uint64:
			argStr = fmt.Sprintf("%v", v)
		case float32, float64:
			argStr = fmt.Sprintf("%v", v)
		case bool:
			if v {
				argStr = "1"
			} else {
				argStr = "0"
			}
		default:
			argStr = fmt.Sprintf("'%v'", v)
		}

		// 替换第一个?占位符
		result = strings.Replace(result, "?", argStr, 1)
	}

	return result
}

// cleanSQLFormat 清理SQL中的格式化字符
func cleanSQLFormat(query string) string {
	// 移除换行符和制表符
	result := strings.ReplaceAll(query, "\n", " ")
	result = strings.ReplaceAll(result, "\t", " ")

	// 移除多余的空格
	for strings.Contains(result, "  ") {
		result = strings.ReplaceAll(result, "  ", " ")
	}

	// 移除首尾空格
	result = strings.TrimSpace(result)

	return result
}
