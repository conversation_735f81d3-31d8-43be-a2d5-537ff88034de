package utils

import (
	"sport-server/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

// SuccessResp 构建成功响应
func SuccessResp(message string) types.BaseResponse {
	if message == "" {
		message = "操作成功"
	}
	return types.BaseResponse{
		Code:     200,
		Message:  message,
		ErrorMsg: "",
	}
}

// ErrorResp 构建错误响应
func ErrorResp(message string, errorMsg string) types.BaseResponse {
	if message == "" {
		message = "操作失败"
	}
	if errorMsg == "" {
		errorMsg = message
	}
	return types.BaseResponse{
		Code:     500,
		Message:  message,
		ErrorMsg: errorMsg,
	}
}

// BuildSuccessResp 构建成功响应，返回通用响应类型
func BuildSuccessResp(data interface{}, message string) *types.UniversalResponse {
	if message == "" {
		message = "操作成功"
	}
	if data == nil {
		data = []interface{}{}
	}
	return &types.UniversalResponse{
		Code:     200,
		Message:  message,
		ErrorMsg: "",
		Data:     data,
	}
}

// BuildErrorResp 构建错误响应，返回通用响应类型（error_msg 为空）
func BuildErrorResp(message string) *types.UniversalResponse {
	if message == "" {
		message = "操作失败"
	}
	return &types.UniversalResponse{
		Code:     500,
		Message:  message,
		ErrorMsg: "", // 普通错误响应，error_msg 为空
		Data:     []interface{}{},
	}
}

// BuildErrorRespWithLog 构建错误响应（带日志），返回通用响应类型
func BuildErrorRespWithLog(err error, logger logx.Logger, message string) *types.UniversalResponse {
	var errorMsg string
	if err != nil {
		errorMsg = err.Error()
		if logger != nil {
			logger.Errorf("%s: %v", message, err)
		}
	}
	if message == "" {
		message = "操作失败"
	}
	return &types.UniversalResponse{
		Code:     500,
		Message:  message,
		ErrorMsg: errorMsg,
		Data:     []interface{}{},
	}
}


