package utils

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// SQLBuilder SQL构建器
type SQLBuilder struct {
	selectFields []string
	fromTable    string
	joins        []string
	whereClause  []string
	args         []interface{}
	groupBy      []string
	having       []string
	orderBy      []string
	limit        string
}

// NewSQLBuilder 创建新的SQL构建器
func NewSQLBuilder() *SQLBuilder {
	return &SQLBuilder{
		selectFields: make([]string, 0),
		joins:        make([]string, 0),
		whereClause:  make([]string, 0),
		args:         make([]interface{}, 0),
		groupBy:      make([]string, 0),
		having:       make([]string, 0),
		orderBy:      make([]string, 0),
	}
}

// Select 设置SELECT字段
func (b *SQLBuilder) Select(fields ...string) *SQLBuilder {
	b.selectFields = append(b.selectFields, fields...)
	return b
}

// From 设置FROM表名
func (b *SQLBuilder) From(table string) *SQLBuilder {
	b.fromTable = table
	return b
}

// Join 添加INNER JOIN
func (b *SQLBuilder) Join(table, condition string) *SQLBuilder {
	join := fmt.Sprintf("INNER JOIN %s ON %s", table, condition)
	b.joins = append(b.joins, join)
	return b
}

// LeftJoin 添加LEFT JOIN
func (b *SQLBuilder) LeftJoin(table, condition string) *SQLBuilder {
	join := fmt.Sprintf("LEFT JOIN %s ON %s", table, condition)
	b.joins = append(b.joins, join)
	return b
}

// Where 添加WHERE条件（总是添加）
func (b *SQLBuilder) Where(condition string, args ...interface{}) *SQLBuilder {
	b.whereClause = append(b.whereClause, condition)
	b.args = append(b.args, args...)
	return b
}

// WhereOptional 通用的可选条件添加方法
func (b *SQLBuilder) WhereOptional(condition bool, clause string, args ...interface{}) *SQLBuilder {
	if condition {
		b.whereClause = append(b.whereClause, clause)
		b.args = append(b.args, args...)
	}
	return b
}

// WhereEq 添加等于条件（当值不为零值时）
func (b *SQLBuilder) WhereEq(field string, value interface{}) *SQLBuilder {
	if !isZeroValue(value) {
		clause := fmt.Sprintf("%s = ?", field)
		b.whereClause = append(b.whereClause, clause)
		b.args = append(b.args, value)
	}
	return b
}

// WhereGt 添加大于条件
func (b *SQLBuilder) WhereGt(field string, value interface{}) *SQLBuilder {
	if !isZeroValue(value) {
		clause := fmt.Sprintf("%s > ?", field)
		b.whereClause = append(b.whereClause, clause)
		b.args = append(b.args, value)
	}
	return b
}

// WhereIn 添加IN条件
func (b *SQLBuilder) WhereIn(field string, values []interface{}) *SQLBuilder {
	if len(values) > 0 {
		placeholders := make([]string, len(values))
		for i := range placeholders {
			placeholders[i] = "?"
		}
		condition := fmt.Sprintf("%s IN (%s)", field, strings.Join(placeholders, ","))
		b.whereClause = append(b.whereClause, condition)
		b.args = append(b.args, values...)
	}
	return b
}

// GroupBy 添加分组
func (b *SQLBuilder) GroupBy(fields ...string) *SQLBuilder {
	b.groupBy = append(b.groupBy, fields...)
	return b
}

// Having 添加HAVING条件
func (b *SQLBuilder) Having(condition string, args ...interface{}) *SQLBuilder {
	b.having = append(b.having, condition)
	b.args = append(b.args, args...)
	return b
}

// OrderBy 添加排序
func (b *SQLBuilder) OrderBy(field string, direction ...string) *SQLBuilder {
	dir := "ASC"
	if len(direction) > 0 {
		dir = direction[0]
	}
	b.orderBy = append(b.orderBy, fmt.Sprintf("%s %s", field, dir))
	return b
}

// Limit 设置限制条数
func (b *SQLBuilder) Limit(count int) *SQLBuilder {
	b.limit = fmt.Sprintf("LIMIT %d", count)
	return b
}

// Build 构建SQL语句和参数
func (b *SQLBuilder) Build() (string, []interface{}) {
	var query strings.Builder

	// SELECT
	if len(b.selectFields) > 0 {
		query.WriteString("SELECT ")
		query.WriteString(strings.Join(b.selectFields, ", "))
	} else {
		query.WriteString("SELECT *")
	}

	// FROM
	if b.fromTable != "" {
		query.WriteString(" FROM ")
		query.WriteString(b.fromTable)
	}

	// JOIN
	if len(b.joins) > 0 {
		for _, join := range b.joins {
			query.WriteString(" ")
			query.WriteString(join)
		}
	}

	// WHERE
	if len(b.whereClause) > 0 {
		query.WriteString(" WHERE ")
		query.WriteString(strings.Join(b.whereClause, " AND "))
	}

	// GROUP BY
	if len(b.groupBy) > 0 {
		query.WriteString(" GROUP BY ")
		query.WriteString(strings.Join(b.groupBy, ", "))
	}

	// HAVING
	if len(b.having) > 0 {
		query.WriteString(" HAVING ")
		query.WriteString(strings.Join(b.having, " AND "))
	}

	// ORDER BY
	if len(b.orderBy) > 0 {
		query.WriteString(" ORDER BY ")
		query.WriteString(strings.Join(b.orderBy, ", "))
	}

	// LIMIT
	if b.limit != "" {
		query.WriteString(" ")
		query.WriteString(b.limit)
	}

	return query.String(), b.args
}

// QueryRows 执行查询并自动处理NULL值转换
func (b *SQLBuilder) QueryRows(ctx context.Context, conn sqlx.SqlConn, dest interface{}) error {
	query, args := b.Build()

	// 创建临时结构体用于接收原始数据（包含sql.Null*类型）
	tempSlice := createTempSliceForQuery(dest)

	// 执行原始查询
	err := conn.QueryRowsCtx(ctx, tempSlice, query, args...)
	if err != nil {
		// 如果是查询不到数据，初始化目标切片为空切片并返回nil
		if err == sqlx.ErrNotFound {
			destValue := reflect.ValueOf(dest).Elem()
			destValue.Set(reflect.MakeSlice(destValue.Type(), 0, 0))
			return nil
		}
		return err
	}

	// 转换NULL值并填充到目标结构体
	return convertNullValues(tempSlice, dest)
}

// QueryRow 执行单行查询并自动处理NULL值转换
func (b *SQLBuilder) QueryRow(ctx context.Context, conn sqlx.SqlConn, dest interface{}) error {
	query, args := b.Build()

	// 创建临时结构体用于接收原始数据
	tempStruct := createTempStructForQuery(dest)

	// 执行原始查询
	err := conn.QueryRowCtx(ctx, tempStruct, query, args...)
	if err != nil {
		// 如果是查询不到数据，不返回错误
		if err == sqlx.ErrNotFound {
			destValue := reflect.ValueOf(dest).Elem()
			destValue.Set(reflect.Zero(destValue.Type()))
			return nil
		}
		return err
	}

	// 转换NULL值并填充到目标结构体
	return convertNullValue(tempStruct, dest)
}

func (b *SQLBuilder) QueryRowReturnNotFound(ctx context.Context, conn sqlx.SqlConn, dest interface{}) error {
	query, args := b.Build()

	// 创建临时结构体用于接收原始数据
	tempStruct := createTempStructForQuery(dest)

	// 执行原始查询
	err := conn.QueryRowCtx(ctx, tempStruct, query, args...)
	if err != nil {
		return err
	}

	// 转换NULL值并填充到目标结构体
	return convertNullValue(tempStruct, dest)
}

// createTempSliceForQuery 为切片查询创建临时结构体
func createTempSliceForQuery(dest interface{}) interface{} {
	destValue := reflect.ValueOf(dest).Elem()
	elemType := destValue.Type().Elem().Elem() // []*Struct -> Struct

	// 创建对应的临时结构体类型（包含sql.Null*字段）
	tempType := createTempStructType(elemType)

	// 创建临时切片
	tempSliceType := reflect.SliceOf(reflect.PtrTo(tempType))
	return reflect.New(tempSliceType).Interface()
}

// createTempStructForQuery 为单行查询创建临时结构体
func createTempStructForQuery(dest interface{}) interface{} {
	destType := reflect.TypeOf(dest).Elem()
	tempType := createTempStructType(destType)
	return reflect.New(tempType).Interface()
}

// createTempStructType 创建包含sql.Null*字段的临时结构体类型
func createTempStructType(originalType reflect.Type) reflect.Type {
	var fields []reflect.StructField

	for i := 0; i < originalType.NumField(); i++ {
		field := originalType.Field(i)

		// 根据原始字段类型创建对应的sql.Null*类型
		var newFieldType reflect.Type
		switch field.Type.Kind() {
		case reflect.String:
			newFieldType = reflect.TypeOf(sql.NullString{})
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			newFieldType = reflect.TypeOf(sql.NullInt64{})
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			newFieldType = reflect.TypeOf(sql.NullInt64{})
		case reflect.Float32, reflect.Float64:
			newFieldType = reflect.TypeOf(sql.NullFloat64{})
		case reflect.Bool:
			newFieldType = reflect.TypeOf(sql.NullBool{})
		case reflect.Ptr:
			// 检查是否是*time.Time类型
			if field.Type == reflect.TypeOf((*time.Time)(nil)) {
				newFieldType = reflect.TypeOf(sql.NullTime{})
			} else {
				// 其他指针类型保持不变，因为它们已经可以处理NULL
				newFieldType = field.Type
			}
		default:
			// 其他类型保持不变
			newFieldType = field.Type
		}

		newField := reflect.StructField{
			Name: field.Name,
			Type: newFieldType,
			Tag:  field.Tag,
		}
		fields = append(fields, newField)
	}

	return reflect.StructOf(fields)
}

// convertNullValues 转换切片中的NULL值
func convertNullValues(tempSlice, dest interface{}) error {
	tempValue := reflect.ValueOf(tempSlice).Elem()
	destValue := reflect.ValueOf(dest).Elem()

	// 清空目标切片
	destValue.Set(reflect.MakeSlice(destValue.Type(), 0, tempValue.Len()))

	// 转换每个元素
	for i := 0; i < tempValue.Len(); i++ {
		tempItem := tempValue.Index(i)

		// 创建目标结构体实例
		destItemType := destValue.Type().Elem().Elem()
		destItem := reflect.New(destItemType)

		// 转换单个结构体
		err := convertNullValue(tempItem.Interface(), destItem.Interface())
		if err != nil {
			return err
		}

		// 添加到目标切片
		destValue.Set(reflect.Append(destValue, destItem))
	}

	return nil
}

// convertNullValue 转换单个结构体的NULL值
func convertNullValue(tempStruct, dest interface{}) error {
	tempValue := reflect.ValueOf(tempStruct).Elem()
	destValue := reflect.ValueOf(dest).Elem()

	for i := 0; i < tempValue.NumField(); i++ {
		tempField := tempValue.Field(i)
		destField := destValue.Field(i)

		// 根据字段类型进行转换
		switch tempField.Type() {
		case reflect.TypeOf(sql.NullString{}):
			nullStr := tempField.Interface().(sql.NullString)
			if nullStr.Valid {
				destField.SetString(nullStr.String)
			} else {
				destField.SetString("") // NULL -> ""
			}

		case reflect.TypeOf(sql.NullInt64{}):
			nullInt := tempField.Interface().(sql.NullInt64)
			if nullInt.Valid {
				// 检查目标字段类型
				switch destField.Kind() {
				case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
					destField.SetUint(uint64(nullInt.Int64))
				default:
					destField.SetInt(nullInt.Int64)
				}
			} else {
				// 检查目标字段类型
				switch destField.Kind() {
				case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
					destField.SetUint(0)
				default:
					destField.SetInt(0)
				}
			}

		case reflect.TypeOf(sql.NullFloat64{}):
			nullFloat := tempField.Interface().(sql.NullFloat64)
			if nullFloat.Valid {
				destField.SetFloat(nullFloat.Float64)
			} else {
				destField.SetFloat(0) // NULL -> 0.0
			}

		case reflect.TypeOf(sql.NullBool{}):
			nullBool := tempField.Interface().(sql.NullBool)
			if nullBool.Valid {
				destField.SetBool(nullBool.Bool)
			} else {
				destField.SetBool(false) // NULL -> false
			}

		case reflect.TypeOf(sql.NullTime{}):
			nullTime := tempField.Interface().(sql.NullTime)
			if nullTime.Valid {
				// 创建time.Time指针并设置值
				timePtr := &nullTime.Time
				destField.Set(reflect.ValueOf(timePtr))
			} else {
				// NULL -> nil
				destField.Set(reflect.Zero(destField.Type()))
			}

		default:
			// 指针类型和其他类型直接复制
			if tempField.CanInterface() && destField.CanSet() {
				destField.Set(tempField)
			}
		}
	}

	return nil
}

// isZeroValue 判断是否为零值
func isZeroValue(value interface{}) bool {
	switch v := value.(type) {
	case string:
		return v == ""
	case int, int8, int16, int32, int64:
		return v == 0
	case uint, uint8, uint16, uint32, uint64:
		return v == 0
	case float32, float64:
		return v == 0
	case bool:
		return !v
	case nil:
		return true
	default:
		return false
	}
}
