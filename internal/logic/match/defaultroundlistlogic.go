package match

import (
	"context"

	"sport-server/internal/constants"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type DefaultRoundListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取各赛事默认轮次
func NewDefaultRoundListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DefaultRoundListLogic {
	return &DefaultRoundListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DefaultRoundListLogic) DefaultRoundList(req *types.DefaultRoundListReq) (resp *types.UniversalResponse, err error) {

	defaultRounds, err := l.svcCtx.FootballMatchModel.FindDefaultRound(l.ctx)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询默认轮次失败"), nil
	}

	if len(defaultRounds) == 0 {
		return utils.BuildSuccessResp([]types.DefaultRoundItem{}, "查询成功"), nil
	}

	//这里这样写是为了前端要求赛事的输出顺序要和前端的顺序一样
	resultMap := make(map[string]types.DefaultRoundItem)
	for _, round := range defaultRounds {
		resultMap[round.CompShortZh] = types.DefaultRoundItem{
			CompShortZh: round.CompShortZh,
			RoundNum:    round.RoundNum,
		}
	}
	var result []types.DefaultRoundItem
	for _, league := range constants.CompShortZhMaps {
		if item, exists := resultMap[league]; exists {
			result = append(result, item)
		}
	}

	// 返回排序后的结果
	return utils.BuildSuccessResp(result, "查询成功"), nil
}
