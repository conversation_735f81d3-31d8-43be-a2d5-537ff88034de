package match

import (
	"context"
	"strconv"
	"time"

	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMatchListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMatchListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMatchListLogic {
	return &GetMatchListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMatchListLogic) GetMatchList(req *types.GetMatchListReq) (resp *types.UniversalResponse, err error) {
	// 查询赛程信息
	schedules, err := l.svcCtx.MatchScheduleModel.FindByCompAndRound(l.ctx, req.CompShortZh, req.RoundNum)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询赛程失败"), nil
	}

	// 查询比赛详情
	matches, err := l.svcCtx.FootballMatchModel.FindByCompAndRound(l.ctx, req.CompShortZh, req.RoundNum)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询比赛详情失败"), nil
	}

	// 获取用户竞猜记录（如果有用户ID）
	var userGuessCount int64
	userId := int64(0)

	// 尝试从context获取用户ID（可能没有，因为这是公开接口）
	if userIdValue := l.ctx.Value("userId"); userIdValue != nil {
		if uid, ok := userIdValue.(int64); ok {
			userId = uid
			userGuessRecords, err := l.svcCtx.UserGuessRecordModel.CountUserGuessInRound(l.ctx, userId, req.CompShortZh, req.RoundNum)
			if err != nil {
				l.Errorf("查询用户竞猜记录失败: %v", err)
			} else {
				userGuessCount = userGuessRecords
			}
		}
	}

	// 构建比赛信息映射
	matchMap := make(map[int]*types.MatchInfo)
	for _, schedule := range schedules {
		if schedule.MatchId == 0 {
			continue
		}

		matchId := schedule.MatchId
		matchInfo := &types.MatchInfo{
			MatchId: matchId,
		}

		// 设置基本信息
		if schedule.MatchTime != 0 {
			matchInfo.MatchTime = time.Unix(schedule.MatchTime, 0).Format("2006-01-02 15:04:05")
		}
		matchInfo.Home = schedule.Home
		matchInfo.HomeLogo = schedule.HomeLogo
		matchInfo.Away = schedule.Away
		matchInfo.AwayLogo = schedule.AwayLogo

		// 处理比分，如果为0则设为nil表示未开始
		if schedule.HomeScore != 0 {
			matchInfo.HomeGoals = &schedule.HomeScore
		}
		if schedule.AwayScore != 0 {
			matchInfo.AwayGoals = &schedule.AwayScore
		}

		matchMap[matchId] = matchInfo
	}

	// 填充比赛详情
	for _, match := range matches {
		matchId := int(match.Id)
		if matchInfo, exists := matchMap[matchId]; exists {
			// 设置让球状态
			matchInfo.HandicapStatus = match.HandicapStatus

			// 设置封盘时间
			if match.CloseTimeSeconds != 0 {
				// 这里需要结合match_time计算实际封盘时间
				for _, schedule := range schedules {
					if schedule.MatchId == matchId && schedule.MatchTime != 0 {
						closeTime := time.Unix(schedule.MatchTime+int64(match.CloseTimeSeconds), 0)
						matchInfo.CloseTime = closeTime.Format("2006-01-02 15:04:05")
						break
					}
				}
			}

			// 初始化竞猜信息
			matchInfo.HandicapGuess = 0
			matchInfo.OverUnderGuess = 0
			matchInfo.ConcedeNum = strconv.FormatFloat(match.ConcedeNum, 'f', 2, 64)
			matchInfo.OverUnderNum = strconv.FormatFloat(match.OverUnderNum, 'f', 2, 64)
			matchInfo.HomeGoals = &match.HomeGoals
			matchInfo.AwayGoals = &match.AwayGoals

		}
	}

	// 如果有用户ID，查询用户的竞猜记录
	var userGuessRecord []*model.UserGuessRecord
	if userId > 0 {
		// 获取所有比赛ID
		var matchIds []int
		for matchId := range matchMap {
			matchIds = append(matchIds, matchId)
		}
		// 查询让球盘竞猜记录
		userGuessRecord, err = l.svcCtx.UserGuessRecordModel.FindByUserAndMatch(l.ctx, userId, matchIds)
		if err != nil {
			l.Errorf("查询用户竞猜记录失败: %v", err)
		}
	}

	var matchList []types.MatchInfo
	for _, schedule := range schedules {
		if schedule.MatchId == 0 {
			continue
		}

		matchId := schedule.MatchId
		if matchInfo, exists := matchMap[matchId]; exists {
			// 填充用户竞猜记录
			for _, userGuessRecordInfo := range userGuessRecord {
				if userGuessRecordInfo.MatchId == 0 {
					continue
				}

				if userGuessRecordInfo.MatchId == matchId {
					if userGuessRecordInfo.Type == constants.GuessTypeHandicap {
						matchInfo.HandicapGuess = userGuessRecordInfo.WhoWin
					} else if userGuessRecordInfo.Type == constants.GuessTypeOverUnder {
						matchInfo.OverUnderGuess = userGuessRecordInfo.WhoWin
					}
				}
			}
			matchList = append(matchList, *matchInfo)
		}
	}

	// 计算RoundStatus
	// 查询所有比赛的handicap_status来判断轮次状态
	roundStatus := calculateRoundStatus(matches)

	return utils.BuildSuccessResp(types.MatchListData{
		List:                   matchList,
		UserGuessCount:         userGuessCount,
		MinGuessCountForReward: constants.MinGuessCountForReward,
		RoundStatus:            roundStatus,
	}, "查询成功"), nil
}

// calculateRoundStatus 计算轮次状态
// 所有数据的handicap_status字段都是1时，RoundStatus =0 未开盘
// 所有数据的handicap_status字段都是3或4时，RoundStatus =2 已结束
// 否则RoundStatus =1 进行中
func calculateRoundStatus(matches []*model.FootballMatch) int {
	if len(matches) == 0 {
		return 1 // 默认进行中
	}

	allNotOpen := true  // 所有都是未开盘(1)
	allFinished := true // 所有都是已结束(3或4)

	for _, match := range matches {
		status := match.HandicapStatus

		// 检查是否全部未开盘
		if status != constants.HandicapStatusNotOpen {
			allNotOpen = false
		}

		// 检查是否全部已结束
		if status != constants.HandicapStatusClosed && status != constants.HandicapStatusSettled {
			allFinished = false
		}
	}

	if allNotOpen {
		return 0 // 未开盘
	}
	if allFinished {
		return 2 // 已结束
	}
	return 1 // 进行中
}
