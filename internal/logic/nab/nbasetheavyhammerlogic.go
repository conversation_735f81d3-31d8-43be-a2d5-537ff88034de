package nab

import (
	"context"
	"time"

	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NbaSetHeavyHammerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNbaSetHeavyHammerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaSetHeavyHammerLogic {
	return &NbaSetHeavyHammerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaSetHeavyHammerLogic) NbaSetHeavyHammer(req *types.NbaSetHeavyHammerReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	record, err := l.svcCtx.UserNabGuessRecordModel.FindOne(l.ctx, req.Id)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询竞猜记录失败"), nil
	}

	if record.UserId != userId {
		return utils.BuildErrorResp("查无此记录"), nil
	}

	// 检查记录的result字段是否等于0
	if record.Result != 0 {
		return utils.BuildErrorResp("只能对未结算的竞猜设置重锤单"), nil
	}

	if record.IsHeavyHammer == 1 {
		return utils.BuildErrorResp("该记录已经是重锤单"), nil
	}

	// 查询比赛信息获取日期
	schedule, err := l.svcCtx.MatchScheduleV2Model.FindOne(l.ctx, int64(record.MatchId))
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询比赛信息失败"), nil
	}

	if schedule.MatchTime == 0 {
		return utils.BuildErrorResp("比赛时间信息不完整"), nil
	}

	matchDate := time.Unix(schedule.MatchTime, 0)
	startOfDay := time.Date(matchDate.Year(), matchDate.Month(), matchDate.Day(), 0, 0, 0, 0, matchDate.Location())
	endOfDay := startOfDay.Add(24*time.Hour - time.Second)

	existingRecords, err := l.svcCtx.UserNabGuessRecordModel.FindMyGuessList(l.ctx, userId,
		func() *int64 { t := startOfDay.Unix(); return &t }(),
		func() *int64 { t := endOfDay.Unix(); return &t }(), 1)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "检查重锤单状态失败"), nil
	}

	for _, existingRecord := range existingRecords {
		if existingRecord.IsHeavyHammer == 1 {
			return utils.BuildErrorResp("该日期已有重锤单，每日最多只能选择1条赛事作为重锤单"), nil
		}
	}

	record.IsHeavyHammer = 1
	record.UpdateTime = time.Now()

	err = l.svcCtx.UserNabGuessRecordModel.Update(l.ctx, record)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "设置重锤单失败"), nil
	}

	return utils.BuildSuccessResp(nil, "设置重锤单成功"), nil
}
