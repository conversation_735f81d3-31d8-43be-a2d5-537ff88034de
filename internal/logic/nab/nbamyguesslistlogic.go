package nab

import (
	"context"
	"strconv"
	"time"

	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NbaMyGuessListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNbaMyGuessListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaMyGuessListLogic {
	return &NbaMyGuessListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaMyGuessListLogic) NbaMyGuessList(req *types.NbaMyGuessListReq) (resp *types.UniversalResponse, err error) {
	userId := int64(0)
	if userIdValue := l.ctx.Value("userId"); userIdValue != nil {
		if uid, ok := userIdValue.(int64); ok {
			userId = uid
		}
	}

	if userId == 0 {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	// 解析日期参数，构建时间范围
	var startTime, endTime *int64
	if req.Date != "" {
		targetDate, err := time.Parse("2006-01-02", req.Date)
		if err != nil {
			return utils.BuildErrorResp("日期格式错误"), nil
		}
		start := targetDate.Unix()
		end := targetDate.Add(24*time.Hour - time.Second).Unix()
		startTime = &start
		endTime = &end
	}
	userGuessRecords, err := l.svcCtx.UserNabGuessRecordModel.FindMyGuessList(l.ctx, userId, startTime, endTime, req.IsOpenResult)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户竞猜记录失败"), nil
	}

	var matchIds []int64
	for _, record := range userGuessRecords {
		matchIds = append(matchIds, int64(record.MatchId))
	}

	var schedules []*model.MatchScheduleV2
	if len(matchIds) > 0 {
		schedules, err = l.svcCtx.MatchScheduleV2Model.FindByMatchIds(l.ctx, matchIds)
		if err != nil {
			return utils.BuildErrorRespWithLog(err, l, "查询比赛信息失败"), nil
		}
	}

	scheduleMap := make(map[int64]*model.MatchScheduleV2)
	for _, schedule := range schedules {
		scheduleMap[schedule.Id] = schedule
	}

	var myGuessList []types.NbaMyGuessItem
	for _, record := range userGuessRecords {
		schedule, exists := scheduleMap[int64(record.MatchId)]
		if !exists {
			continue
		}

		item := types.NbaMyGuessItem{
			Id:            record.Id,
			MatchId:       record.MatchId,
			ConcedeNum:    schedule.ConcedeNum,
			OverUnderNum:  schedule.OverUnderNum,
			WhoWin:        record.WhoWin,
			Result:        record.Result,
			IsHeavyHammer: record.IsHeavyHammer,
			Type:          record.Type,
		}

		item.Home = schedule.Home
		item.Away = schedule.Away
		item.HomeLogo = schedule.HomeLogo
		item.AwayLogo = schedule.AwayLogo
		if schedule.MatchTime != 0 {
			item.MatchTimeFormat = time.Unix(schedule.MatchTime, 0).Format("2006-01-02 15:04:05")
		}

		if record.IsHeavyHammer == 1 {
			item.IsCanHeavyHammer = constants.CanNotHeavyHammer
		} else {
			item.IsCanHeavyHammer = constants.CanHeavyHammer
		}

		if schedule.HomeGoals != 0 {
			item.HomeGoals = strconv.Itoa(schedule.HomeGoals)
		}
		if schedule.AwayGoals != 0 {
			item.AwayGoals = strconv.Itoa(schedule.AwayGoals)
		}

		myGuessList = append(myGuessList, item)
	}

	return utils.BuildSuccessResp(myGuessList, "查询成功"), nil
}
