package nab

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type NbaExchangeCardGroupLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NBA卡组兑换
func NewNbaExchangeCardGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaExchangeCardGroupLogic {
	return &NbaExchangeCardGroupLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaExchangeCardGroupLogic) NbaExchangeCardGroup(req *types.NbaExchangeCardGroupReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	cardGroup, err := l.svcCtx.NabCardGroupsModel.FindOne(l.ctx, int64(req.NabCardGroupsId))
	if err != nil {
		if err == model.ErrNotFound {
			return utils.BuildErrorResp("卡组不存在"), nil
		}
		return utils.BuildErrorRespWithLog(err, l, "查询卡组信息失败"), nil
	}

	cardIdStrs := strings.Split(cardGroup.NbaCardIds, ",")
	var cardIds []int
	for _, idStr := range cardIdStrs {
		if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
			cardIds = append(cardIds, id)
		}
	}

	if len(cardIds) == 0 {
		return utils.BuildErrorResp("卡组配置错误"), nil
	}

	userCards, err := l.svcCtx.UserNabCardModel.FindByUserId(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户卡片失败"), nil
	}

	userCardMap := make(map[int]int)
	for _, userCard := range userCards {
		userCardMap[userCard.NbaCardId] = userCard.Num
	}

	for _, cardId := range cardIds {
		if userCardMap[cardId] < 1 {
			return utils.BuildErrorResp("卡片数量不足，无法兑换"), nil
		}
	}

	// 执行兑换事务
	err = l.executeExchangeTransaction(userId, cardGroup, cardIds)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "兑换事务执行失败"), nil
	}

	rewardDesc := l.buildRewardDescription(cardGroup.RewardType, cardGroup.RewardValue)

	return utils.BuildSuccessResp(map[string]interface{}{
		"reward_description": rewardDesc,
	}, "兑换成功"), nil
}

func (l *NbaExchangeCardGroupLogic) executeExchangeTransaction(userId int64, cardGroup *model.NabCardGroups, cardIds []int) error {
	var conn = l.svcCtx.GetConn()
	return conn.TransactCtx(context.Background(), func(ctx context.Context, session sqlx.Session) error {
		for _, cardId := range cardIds {
			_, err := session.ExecCtx(ctx, "UPDATE `db_user_nab_card` SET num = num - 1 WHERE user_id = ? AND nba_card_id = ? AND num > 0", userId, cardId)
			if err != nil {
				return fmt.Errorf("减少用户卡片数量失败: %v", err)
			}
		}

		_, err := session.ExecCtx(ctx,
			"INSERT INTO `db_user_nabexchange_record` (user_id, nab_card_groups_id, reward_type, reward_value) VALUES (?, ?, ?, ?)",
			userId, cardGroup.Id, cardGroup.RewardType, cardGroup.RewardValue)
		if err != nil {
			return fmt.Errorf("插入兑换记录失败: %v", err)
		}

		return nil
	})
}

func (l *NbaExchangeCardGroupLogic) buildRewardDescription(rewardType int, rewardValue string) string {
	switch rewardType {
	case constants.RewardTypeMoney:
		return rewardValue + "彩金"
	case constants.RewardTypeCoupon:
		return rewardValue + "%存送券"
	default:
		return "未知奖励"
	}
}
