package nab

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NabmatchlistLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNabmatchlistLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NabmatchlistLogic {
	return &NabmatchlistLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NabmatchlistLogic) Nabmatchlist(req *types.NbaGetMatchListReq) (resp *types.UniversalResponse, err error) {
	userId := int64(0)
	if userIdValue := l.ctx.Value("userId"); userIdValue != nil {
		if uid, ok := userIdValue.(int64); ok {
			userId = uid
		}
	}

	now := time.Now()
	currentHour := now.Hour()

	var targetStartTime, targetEndTime int64
	var targetDate string

	if currentHour < 17 {
		tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
		targetStartTime = tomorrow.Unix()
		targetEndTime = tomorrow.Add(24*time.Hour - time.Second).Unix()
		targetDate = tomorrow.Format("2006-01-02")
	} else {
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		targetStartTime = today.Unix()
		targetEndTime = today.Add(24*time.Hour - time.Second).Unix()
		targetDate = today.Format("2006-01-02")
	}

	schedules, err := l.svcCtx.MatchScheduleV2Model.FindByDateRange(l.ctx, targetStartTime, targetEndTime)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询赛程失败"), nil
	}

	var matchList []types.NbaMatchInfo
	var matchIds []int

	for _, schedule := range schedules {
		matchIds = append(matchIds, int(schedule.Id))
	}

	// 查询用户的竞猜记录
	var userGuessRecords []*model.UserNabGuessRecord
	var userGuessCount int64
	if userId > 0 && len(matchIds) > 0 {
		userGuessRecords, err = l.svcCtx.UserNabGuessRecordModel.FindByUserAndMatchIds(l.ctx, userId, matchIds)
		if err != nil {
			l.Errorf("查询用户竞猜记录失败: %v", err)
		}
		userGuessCount = int64(len(userGuessRecords))
	}

	// 构建用户竞猜记录映射 - 按match_id和type分组
	userGuessMap := make(map[string]*model.UserNabGuessRecord)
	for _, record := range userGuessRecords {
		key := fmt.Sprintf("%d_%d", record.MatchId, record.Type)
		userGuessMap[key] = record
	}

	for _, schedule := range schedules {
		matchInfo := types.NbaMatchInfo{
			MatchId:        int(schedule.Id),
			HandicapStatus: schedule.HandicapStatus,
			ConcedeNum:     strconv.FormatFloat(schedule.ConcedeNum, 'f', 2, 64),
			OverUnderNum:   strconv.FormatFloat(schedule.OverUnderNum, 'f', 2, 64),
			HandicapGuess:  0,
			OverUnderGuess: 0,
		}

		if schedule.MatchTime != 0 {
			matchInfo.MatchTime = time.Unix(schedule.MatchTime, 0).Format("2006-01-02 15:04:05")
		}
		matchInfo.Home = schedule.Home
		matchInfo.HomeLogo = schedule.HomeLogo
		matchInfo.Away = schedule.Away
		matchInfo.AwayLogo = schedule.AwayLogo

		if schedule.CloseTimeSeconds > 0 && schedule.MatchTime != 0 {
			closeTime := time.Unix(schedule.MatchTime+int64(schedule.CloseTimeSeconds), 0)
			matchInfo.CloseTime = closeTime.Format("2006-01-02 15:04:05")
		}

		matchInfo.HomeGoals = &schedule.HomeGoals
		matchInfo.AwayGoals = &schedule.AwayGoals

		if handicapGuess, exists := userGuessMap[fmt.Sprintf("%d_%d", int(schedule.Id), constants.GuessTypeHandicap)]; exists {
			matchInfo.HandicapGuess = handicapGuess.WhoWin
		}
		if overUnderGuess, exists := userGuessMap[fmt.Sprintf("%d_%d", int(schedule.Id), constants.GuessTypeOverUnder)]; exists {
			matchInfo.OverUnderGuess = overUnderGuess.WhoWin
		}

		matchList = append(matchList, matchInfo)
	}

	return utils.BuildSuccessResp(types.NbaMatchListData{
		List:           matchList,
		UserGuessCount: userGuessCount,
		Date:           targetDate,
	}, "查询成功"), nil
}
