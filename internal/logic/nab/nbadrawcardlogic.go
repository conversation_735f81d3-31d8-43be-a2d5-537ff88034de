package nab

import (
	"context"
	"fmt"
	"time"

	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type NbaDrawCardLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NBA抽卡
func NewNbaDrawCardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaDrawCardLogic {
	return &NbaDrawCardLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaDrawCardLogic) NbaDrawCard(req *types.NbaDrawCardReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	today := time.Now().Format("2006-01-02")

	user, err := l.svcCtx.UserModel.FindOne(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户信息失败"), nil
	}

	if user.NbaLotteryRemainCount <= 0 {
		return utils.BuildErrorResp("NBA抽奖次数不足"), nil
	}

	userNabDate, err := l.svcCtx.UserNabDateModel.FindByUserAndDate(l.ctx, userId, today)
	if err != nil && err != model.ErrNotFound {
		return utils.BuildErrorRespWithLog(err, l, "查询用户抽奖记录失败"), nil
	}

	if err == model.ErrNotFound {
		return utils.BuildErrorResp("今日暂无抽奖次数"), nil
	}

	if userNabDate.LotteryCount >= userNabDate.ExchangeLimitCount {
		return utils.BuildErrorResp("今日抽奖次数已用完"), nil
	}

	drawnCard, err := l.drawCardByProbability()
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "抽卡失败"), nil
	}

	err = l.executeDrawCardTransaction(userId, today, drawnCard.Id)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "抽卡事务执行失败"), nil
	}

	cardData := map[string]interface{}{
		"id":        drawnCard.Id,
		"name_zh":   drawnCard.NameZh,
		"img_url":   drawnCard.ImgUrl,
		"score":     drawnCard.Score,
		"level":     drawnCard.Level,
		"team_name": drawnCard.TeamName,
		"location":  drawnCard.Location,
	}

	return utils.BuildSuccessResp(cardData, "抽卡成功"), nil
}

func (l *NbaDrawCardLogic) executeDrawCardTransaction(userId int64, today string, cardId int) error {
	var conn = l.svcCtx.GetConn()
	err := conn.TransactCtx(context.Background(), func(ctx context.Context, session sqlx.Session) error {
		_, err := session.ExecCtx(ctx, "UPDATE `db_user` SET nba_lottery_remain_count = nba_lottery_remain_count - 1 WHERE id = ?", userId)
		if err != nil {
			return fmt.Errorf("减少用户NBA抽奖次数失败: %v", err)
		}

		_, err = session.ExecCtx(ctx, "UPDATE `db_user_nab_date` SET lottery_count = lottery_count + 1 WHERE user_id = ? AND date = ?", userId, today)
		if err != nil {
			return fmt.Errorf("更新当日抽奖次数失败: %v", err)
		}

		result, err := session.ExecCtx(ctx, "UPDATE `db_user_nab_card` SET num = num + 1 WHERE user_id = ? AND nba_card_id = ?", userId, cardId)
		if err != nil {
			return fmt.Errorf("更新用户卡片失败: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("检查更新结果失败: %v", err)
		}

		if rowsAffected == 0 {
			_, err = session.ExecCtx(ctx, "INSERT INTO `db_user_nab_card` (user_id, nba_card_id, num) VALUES (?, ?, 1)", userId, cardId)
			if err != nil {
				return fmt.Errorf("插入用户卡片记录失败: %v", err)
			}
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("抽卡事务执行失败: %v", err)
	}

	return nil
}

// 根据概率抽取卡片
func (l *NbaDrawCardLogic) drawCardByProbability() (*model.NbaCard, error) {
	// 生成1-10000的随机数
	rand := time.Now().UnixNano()%10000 + 1

	var level string
	// S级 1.5% (1-150)
	if rand <= 150 {
		level = "S"
	} else if rand <= 1000 { // A级 8.5% (151-1000)
		level = "A"
	} else if rand <= 4500 { // B级 35% (1001-4500)
		level = "B"
	} else { // C级 55% (4501-10000)
		level = "C"
	}

	// 根据等级获取卡片列表
	cards, err := l.svcCtx.NbaCardModel.FindByLevel(l.ctx, level)
	if err != nil {
		return nil, err
	}

	if len(cards) == 0 {
		return nil, fmt.Errorf("没有找到%s级卡片", level)
	}

	// 随机选择一张卡片
	cardIndex := time.Now().UnixNano() % int64(len(cards))
	return cards[cardIndex], nil
}
