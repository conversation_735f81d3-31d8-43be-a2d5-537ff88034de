package nab

import (
	"context"
	"strconv"
	"strings"

	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NbaGetCardGroupListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取NBA卡组列表
func NewNbaGetCardGroupListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaGetCardGroupListLogic {
	return &NbaGetCardGroupListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaGetCardGroupListLogic) NbaGetCardGroupList(req *types.NbaGetCardGroupListReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	cardGroups, err := l.svcCtx.NabCardGroupsModel.FindAll(l.ctx)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询卡组列表失败"), nil
	}

	userCards, err := l.svcCtx.UserNabCardModel.FindByUserId(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户卡片失败"), nil
	}

	userCardMap := make(map[int]int)
	for _, userCard := range userCards {
		userCardMap[userCard.NbaCardId] = userCard.Num
	}

	allCardIds := make(map[int64]bool)
	groupCardIds := make(map[int64][]int)

	for i, group := range cardGroups {
		cardIdStrs := strings.Split(group.NbaCardIds, ",")
		var cardIds []int
		for _, idStr := range cardIdStrs {
			if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
				cardIds = append(cardIds, id)
				allCardIds[int64(id)] = true
			}
		}
		groupCardIds[int64(i)] = cardIds
	}

	// 批量查询所有需要的卡片
	var cardIdList []int64
	for cardId := range allCardIds {
		cardIdList = append(cardIdList, cardId)
	}

	allCards, err := l.svcCtx.NbaCardModel.FindByIds(l.ctx, cardIdList)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询卡片详情失败"), nil
	}

	cardMap := make(map[int]*model.NbaCard)
	for _, card := range allCards {
		cardMap[int(card.Id)] = card
	}

	var result []map[string]interface{}
	for i, group := range cardGroups {
		cardIds := groupCardIds[int64(i)]

		// 构建卡片详情
		var cards []map[string]interface{}
		canExchange := 1 // 默认可以兑换，如果有任何一张卡片数量为0则不能兑换

		for _, cardId := range cardIds {
			card, exists := cardMap[cardId]
			if !exists {
				continue // 跳过不存在的卡片
			}

			ownedCount := userCardMap[cardId]
			// 如果任何一张卡片的拥有数量为0，则不能兑换
			if ownedCount <= 0 {
				canExchange = 0
			}

			cardData := map[string]interface{}{
				"id":          card.Id,
				"name_zh":     card.NameZh,
				"img_url":     card.ImgUrl,
				"score":       card.Score,
				"level":       card.Level,
				"team_name":   card.TeamName,
				"location":    card.Location,
				"owned_count": ownedCount,
			}
			cards = append(cards, cardData)
		}

		// 如果卡组中没有卡片，也不能兑换
		if len(cards) == 0 {
			canExchange = 0
		}

		groupData := map[string]interface{}{
			"id":              group.Id,
			"title":           group.Title,
			"reward_type":     group.RewardType,
			"reward_value":    group.RewardValue,
			"reward_title":    utils.GetRewardTitle(group.RewardType, group.RewardValue),
			"is_can_exchange": canExchange,
			"cards":           cards,
		}
		result = append(result, groupData)
	}

	return utils.BuildSuccessResp(result, "查询成功"), nil
}
