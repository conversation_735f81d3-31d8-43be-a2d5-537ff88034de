package nab

import (
	"context"
	"fmt"
	"time"

	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NbaSubmitGuessLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNbaSubmitGuessLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaSubmitGuessLogic {
	return &NbaSubmitGuessLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaSubmitGuessLogic) NbaSubmitGuess(req *types.NbaSubmitGuessReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	matchIds := make([]int64, 0, len(req.Guesses))
	for _, guess := range req.Guesses {
		matchIds = append(matchIds, int64(guess.MatchId))
	}

	schedules, err := l.svcCtx.MatchScheduleV2Model.FindByMatchIds(l.ctx, matchIds)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询比赛信息失败"), nil
	}

	scheduleMap := make(map[int64]*model.MatchScheduleV2)
	for _, schedule := range schedules {
		scheduleMap[schedule.Id] = schedule
	}

	var allGuesses []*model.UserNabGuessRecord
	now := time.Now()

	haveCloseTime := 0
	for _, guess := range req.Guesses {
		schedule, exists := scheduleMap[int64(guess.MatchId)]
		if !exists {
			l.Infof("比赛 %d 不存在，跳过竞猜", guess.MatchId)
			continue
		}

		if schedule.CloseTimeSeconds > 0 && schedule.MatchTime != 0 {
			closeTime := time.Unix(schedule.MatchTime+int64(schedule.CloseTimeSeconds), 0)
			if now.After(closeTime) {
				haveCloseTime = 1
				l.Infof("比赛 %d 已封盘，跳过竞猜", guess.MatchId)
				continue
			}
		}

		if !constants.ValidGuessTypes[guess.Type] {
			l.Infof("无效的竞猜类型 %d，跳过", guess.Type)
			continue
		}

		if !constants.ValidWhoWinTypes[guess.WhoWin] {
			l.Infof("无效的竞猜结果 %d，跳过", guess.WhoWin)
			continue
		}

		// 构建竞猜记录
		record := &model.UserNabGuessRecord{
			UserId:        userId,
			MatchId:       guess.MatchId,
			Type:          guess.Type,
			RewardAmount:  0.00,
			IsHeavyHammer: 0,
			WhoWin:        guess.WhoWin,
		}
		allGuesses = append(allGuesses, record)
	}

	existingMatchIds := make([]int, 0, len(allGuesses))
	for _, record := range allGuesses {
		existingMatchIds = append(existingMatchIds, record.MatchId)
	}

	existingRecords, err := l.svcCtx.UserNabGuessRecordModel.FindByUserAndMatchIds(l.ctx, userId, existingMatchIds)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "检查竞猜记录失败"), nil
	}

	existingMap := make(map[string]bool)
	for _, existing := range existingRecords {
		key := fmt.Sprintf("%d_%d", existing.MatchId, existing.Type)
		existingMap[key] = true
	}

	var validGuesses []*model.UserNabGuessRecord
	for _, record := range allGuesses {
		key := fmt.Sprintf("%d_%d", record.MatchId, record.Type)
		if !existingMap[key] {
			validGuesses = append(validGuesses, record)
		} else {
			l.Infof("用户 %d 已对比赛 %d 类型 %d 进行过竞猜，跳过", userId, record.MatchId, record.Type)
		}
	}

	if haveCloseTime == 1 {
		if len(validGuesses) > 0 {
			err = l.svcCtx.UserNabGuessRecordModel.BatchInsert(l.ctx, validGuesses)
			if err != nil {
				return utils.BuildErrorRespWithLog(err, l, "提交竞猜失败"), nil
			}
		}
		return utils.BuildErrorResp("该赛事已封盘，请重新提交"), nil
	}

	if len(validGuesses) == 0 {
		return utils.BuildErrorResp("当前提交无任何可再参与的竞猜"), nil
	}

	// 批量插入竞猜记录
	err = l.svcCtx.UserNabGuessRecordModel.BatchInsert(l.ctx, validGuesses)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "提交竞猜失败"), nil
	}

	return utils.BuildSuccessResp(nil, "提交成功"), nil
}
