package nab

import (
	"context"

	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type NbaGetStarListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取NBA球星列表
func NewNbaGetStarListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NbaGetStarListLogic {
	return &NbaGetStarListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NbaGetStarListLogic) NbaGetStarList(req *types.NbaGetStarListReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	var cards []*model.NbaCard
	if req.Level != "" {
		cards, err = l.svcCtx.NbaCardModel.FindByLevel(l.ctx, req.Level)
	} else {
		cards, err = l.svcCtx.NbaCardModel.FindAll(l.ctx)
	}

	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询NBA卡片失败"), nil
	}

	userCards, err := l.svcCtx.UserNabCardModel.FindByUserId(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户卡片失败"), nil
	}

	userCardMap := make(map[int]int)
	for _, userCard := range userCards {
		userCardMap[userCard.NbaCardId] = userCard.Num
	}

	var result []map[string]interface{}
	for _, card := range cards {
		ownedCount := userCardMap[card.Id]
		cardData := map[string]interface{}{
			"id":          card.Id,
			"name_zh":     card.NameZh,
			"img_url":     card.ImgUrl,
			"score":       card.Score,
			"level":       card.Level,
			"team_name":   card.TeamName,
			"location":    card.Location,
			"owned_count": ownedCount,
		}
		result = append(result, cardData)
	}

	return utils.BuildSuccessResp(result, "查询成功"), nil
}
