package guess

import (
	"context"
	"fmt"
	"sport-server/internal/constants"
	"strconv"
	"time"

	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type MyGuessListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMyGuessListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MyGuessListLogic {
	return &MyGuessListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MyGuessListLogic) MyGuessList(req *types.MyGuessListReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	records, err := l.svcCtx.UserGuessRecordModel.FindMyGuessList(l.ctx, userId, req.CompShortZh, req.RoundNum, req.IsOpenResult)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询竞猜记录失败"), nil
	}

	if len(records) == 0 {
		return utils.BuildSuccessResp([]types.MyGuessItem{}, "查询成功"), nil
	}

	// 收集数据并构建重锤单检查map
	compShortZhSet := make(map[string]bool)
	roundNumSet := make(map[int]bool)
	matchIds := make([]int, 0, len(records))
	matchIdsInt64 := make([]int64, 0, len(records))

	for _, record := range records {
		compShortZhSet[record.CompShortZh] = true
		roundNumSet[record.RoundNum] = true
		if record.MatchId != 0 {
			matchIds = append(matchIds, record.MatchId)
			matchIdsInt64 = append(matchIdsInt64, int64(record.MatchId))
		}
	}

	compShortZhList := make([]string, 0, len(compShortZhSet))
	roundNumList := make([]int, 0, len(roundNumSet))
	for compShortZh := range compShortZhSet {
		compShortZhList = append(compShortZhList, compShortZh)
	}
	for roundNum := range roundNumSet {
		roundNumList = append(roundNumList, roundNum)
	}

	heavyHammerRecords, err := l.svcCtx.UserGuessRecordModel.SelectCheckHeavyHammers(l.ctx, userId, compShortZhList, roundNumList)
	if err != nil {
		l.Errorf("查询重锤单记录失败: %v", err)
		heavyHammerRecords = []*model.HeavyHammerCheck{}
	}

	heavyHammerMap := make(map[string]bool, len(heavyHammerRecords))
	for _, record := range heavyHammerRecords {
		heavyHammerMap[fmt.Sprintf("%s_%d", record.CompShortZh, record.RoundNum)] = true
	}

	guessItems := make([]types.MyGuessItem, 0, len(records))
	if len(matchIds) == 0 {
		return utils.BuildSuccessResp(guessItems, "查询成功"), nil
	}

	schedules, err := l.svcCtx.MatchScheduleModel.FindByMatchIds(l.ctx, matchIds)
	if err != nil {
		l.Errorf("批量查询赛程信息失败: %v", err)
	}

	matches, err := l.svcCtx.FootballMatchModel.FindByIds(l.ctx, matchIdsInt64)
	if err != nil {
		l.Errorf("批量查询比赛信息失败: %v", err)
	}

	scheduleMap := make(map[int]*model.MatchSchedule, len(schedules))
	for _, schedule := range schedules {
		scheduleMap[schedule.MatchId] = schedule
	}

	matchMap := make(map[int]*model.FootballMatch, len(matches))
	for _, match := range matches {
		matchMap[int(match.Id)] = match
	}

	// 组合数据
	for _, record := range records {
		item := types.MyGuessItem{
			Id:            record.Id,
			Result:        record.Result,
			WhoWin:        record.WhoWin,
			Type:          record.Type,
			IsHeavyHammer: record.IsHeavyHammer,
		}

		if schedule, exists := scheduleMap[record.MatchId]; exists {
			item.MatchId = schedule.MatchId
			item.Home = schedule.Home
			item.Away = schedule.Away
			item.HomeLogo = schedule.HomeLogo
			item.AwayLogo = schedule.AwayLogo
			item.CompShortZh = schedule.CompShortZh
			item.MatchTimeFormat = time.Unix(schedule.MatchTime, 0).Format("2006-01-02 15:04:05")
		}

		if match, exists := matchMap[record.MatchId]; exists {
			item.ConcedeNum = match.ConcedeNum
			item.OverUnderNum = match.OverUnderNum
			item.RoundNum = match.RoundNum
			item.HomeGoals = strconv.Itoa(match.HomeGoals)
			item.AwayGoals = strconv.Itoa(match.AwayGoals)
			if req.IsOpenResult == 1 {
				item.HomeGoals = "-"
				item.AwayGoals = "-"
			}

		}

		// 检查是否能设置重锤单
		heavyHammerKey := fmt.Sprintf("%s_%d", item.CompShortZh, item.RoundNum)
		if heavyHammerMap[heavyHammerKey] {
			item.IsCanHeavyHammer = constants.CanNotHeavyHammer
		} else {
			item.IsCanHeavyHammer = constants.CanHeavyHammer
		}

		guessItems = append(guessItems, item)
	}

	return utils.BuildSuccessResp(guessItems, "查询成功"), nil
}
