package guess

import (
	"context"
	"fmt"
	"time"

	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubmitGuessLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSubmitGuessLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitGuessLogic {
	return &SubmitGuessLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubmitGuessLogic) SubmitGuess(req *types.SubmitGuessReq) (resp *types.UniversalResponse, err error) {
	// 从context中获取用户ID
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return utils.BuildErrorResp("用户未登录"), nil
	}

	// 首先检查所有match_id是否在db_matches_schedule表中存在
	matchIds := make([]int, 0, len(req.Guesses))
	for _, guess := range req.Guesses {
		matchIds = append(matchIds, guess.MatchId)
	}

	matchExistsMap, err := l.svcCtx.MatchScheduleModel.CheckMatchExists(l.ctx, matchIds)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "检查比赛信息失败"), nil
	}

	// 构建所有可能的竞猜记录
	var allGuesses []*model.UserGuessRecord
	now := time.Now()

	haveCloseTime := 0
	for _, guess := range req.Guesses {
		// 检查match_id是否存在
		if !matchExistsMap[guess.MatchId] {
			l.Infof("比赛 %d 不存在，跳过竞猜", guess.MatchId)
			continue
		}

		// 检查封盘时间
		closeTime, err := l.svcCtx.FootballMatchModel.GetCloseTime(l.ctx, guess.MatchId)
		if err != nil {
			l.Errorf("获取比赛封盘时间失败: %v", err)
			continue
		}

		// 如果当前时间已经超过封盘时间，跳过这个竞猜
		if now.After(closeTime) {
			haveCloseTime = 1
			l.Infof("比赛 %d 已封盘，跳过竞猜", guess.MatchId)
			continue
		}

		// 获取比赛的联赛和轮次信息
		schedule, err := l.svcCtx.MatchScheduleModel.FindByMatchId(l.ctx, guess.MatchId)
		if err != nil {
			l.Errorf("获取比赛信息失败: %v", err)
			continue
		}

		// 构建竞猜记录
		record := &model.UserGuessRecord{
			UserId:        userId,
			MatchId:       guess.MatchId,
			Type:          guess.Type,
			RewardAmount:  0.00,
			IsHeavyHammer: 0,
			WhoWin:        guess.WhoWin,
		}

		// 设置联赛和轮次信息
		record.CompShortZh = schedule.CompShortZh

		// 从db_football_match表获取轮次信息
		match, err := l.svcCtx.FootballMatchModel.FindOne(l.ctx, int64(guess.MatchId))
		if err != nil {
			l.Errorf("获取比赛轮次信息失败: %v", err)
			continue
		}

		record.RoundNum = match.RoundNum

		allGuesses = append(allGuesses, record)
	}

	// 检查哪些记录已经存在
	existingMap, err := l.svcCtx.UserGuessRecordModel.CheckExistingRecords(l.ctx, userId, allGuesses)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "检查竞猜记录失败"), nil
	}

	// 过滤出不存在的记录
	var validGuesses []*model.UserGuessRecord
	for _, record := range allGuesses {
		matchId := record.MatchId
		key := fmt.Sprintf("%d_%d", matchId, record.Type)

		if !existingMap[key] {
			validGuesses = append(validGuesses, record)
		} else {
			l.Infof("用户 %d 已对比赛 %d 类型 %d 进行过竞猜，跳过", userId, matchId, record.Type)
		}
	}
	if haveCloseTime == 1 {
		if len(validGuesses) > 0 {
			err = l.svcCtx.UserGuessRecordModel.BatchInsert(l.ctx, validGuesses)
			if err != nil {
				return utils.BuildErrorRespWithLog(err, l, "提交竞猜失败"), nil
			}
		}
		return utils.BuildErrorResp("该赛事已封盘，请重新提交"), nil
	}

	// 如果没有有效的竞猜，返回错误
	if len(validGuesses) == 0 {
		return utils.BuildErrorResp("当前提交无任何可再参与的竞猜"), nil
	}

	// 批量插入竞猜记录
	err = l.svcCtx.UserGuessRecordModel.BatchInsert(l.ctx, validGuesses)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "提交竞猜失败"), nil
	}

	return utils.BuildSuccessResp(nil, "提交成功"), nil
}
