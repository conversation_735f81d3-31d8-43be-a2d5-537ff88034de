package guess

import (
	"context"
	"time"

	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetHeavyHammerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetHeavyHammerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetHeavyHammerLogic {
	return &SetHeavyHammerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetHeavyHammerLogic) SetHeavyHammer(req *types.SetHeavyHammerReq) (resp *types.UniversalResponse, err error) {
	// 查询竞猜记录
	record, err := l.svcCtx.UserGuessRecordModel.FindOne(l.ctx, req.UserGuessRecordId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询竞猜记录失败"), nil
	}

	// 检查记录的result字段是否等于0
	if record.Result != 0 {
		return utils.BuildErrorResp("只能对未结算的竞猜设置重锤单"), nil
	}

	// 检查用户在该联赛该轮次是否已有重锤单
	compShortZh := record.CompShortZh
	roundNum := record.RoundNum

	hasHeavyHammer, err := l.svcCtx.UserGuessRecordModel.CheckHeavyHammerExists(l.ctx, record.UserId, compShortZh, roundNum)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "检查重锤单状态失败"), nil
	}

	if hasHeavyHammer {
		return utils.BuildErrorResp("该联赛该轮次已有重锤单，每轮每个联赛最多只能选择1条赛事作为重锤单"), nil
	}

	// 设置为重锤单
	record.IsHeavyHammer = 1
	record.UpdateTime = time.Now()

	err = l.svcCtx.UserGuessRecordModel.Update(l.ctx, record)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "设置重锤单失败"), nil
	}

	return utils.BuildSuccessResp(nil, "设置重锤单成功"), nil
}
