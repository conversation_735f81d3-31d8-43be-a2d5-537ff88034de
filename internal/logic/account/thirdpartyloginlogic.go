package account

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"sport-server/internal/constants"
	"sport-server/internal/model"
	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"
)

type ThirdpartyloginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	w      http.ResponseWriter
	r      *http.Request
}

func NewThirdpartyloginLogic(ctx context.Context, svcCtx *svc.ServiceContext, w http.ResponseWriter, r *http.Request) *ThirdpartyloginLogic {
	return &ThirdpartyloginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		w:      w,
		r:      r,
	}
}

func (l *ThirdpartyloginLogic) Thirdpartylogin(req *types.ThirdPartyLoginReq) (resp *types.UniversalResponse, err error) {

	if !constants.IsValidPlatformId(req.PlatformId) {
		return utils.BuildErrorResp("不支持的平台类型"), nil
	}

	//特殊原因，由于台湾那边有可能出现多个用户同个userid，所以这里加多个name判断
	existingUser, err := l.svcCtx.UserModel.ExistsByThirdUserAndPlatform(
		l.ctx,
		strconv.FormatInt(req.UserId, 10),
		req.Name,
		req.PlatformId,
	)

	var user *model.User
	if err == model.ErrNotFound {
		newUser := &model.User{
			ThirdUserId: strconv.FormatInt(req.UserId, 10),
			Username:    req.Name,
			PlatformId:  req.PlatformId,
		}

		result, err := l.svcCtx.UserModel.Insert(l.ctx, newUser)
		if err != nil {
			return utils.BuildErrorRespWithLog(err, l, "创建用户失败"), nil
		}

		userId, _ := result.LastInsertId()
		newUser.Id = userId
		user = newUser

	} else if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户失败"), nil
	} else {
		user = existingUser
	}

	token, err := l.generateJWTToken(user)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "生成Token失败"), nil
	}

	activityConfig, err := l.svcCtx.SystemConfigModel.FindByConfigKey(l.ctx, "activity_url")
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "获取活动页面配置失败"), nil
	}

	finalUrl := fmt.Sprintf("%s?token=%s&name=%s", activityConfig.ConfigValue, token, user.Username)

	//  开发环境设置Cookie
	if l.svcCtx.Config.Mode == "dev" {
		l.setTokenCookie(token)
	}

	data := types.ThirdPartyLoginData{
		Url: finalUrl,
	}
	return utils.BuildSuccessResp(data, "登录成功"), nil
}

func (l *ThirdpartyloginLogic) generateJWTToken(user *model.User) (string, error) {
	// JWT Claims
	claims := jwt.MapClaims{
		"user_id":       user.Id,
		"third_user_id": user.ThirdUserId,
		"username":      user.Username,
		"platform_id":   user.PlatformId,
		"exp":           time.Now().Add(7 * 24 * time.Hour).Unix(), // 7天过期
		"iat":           time.Now().Unix(),
	}

	// 创建 Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名
	tokenString, err := token.SignedString([]byte(l.svcCtx.Config.Auth.AccessSecret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

func (l *ThirdpartyloginLogic) setTokenCookie(token string) {
	// 设置Cookie（仅开发环境）
	cookie := &http.Cookie{
		Name:     "auth_token",
		Value:    token,
		Path:     "/",
		MaxAge:   7 * 24 * 3600, // 7天
		HttpOnly: true,
		Secure:   false, // 开发环境使用HTTP
		SameSite: http.SameSiteLaxMode,
	}

	// 通过响应头设置Cookie
	l.w.Header().Add("Set-Cookie", cookie.String())
}
