package account

import (
	"context"
	"strconv"

	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type DetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户信息
func NewDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DetailLogic {
	return &DetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DetailLogic) Detail(req *types.UserDetailReq) (resp *types.UniversalResponse, err error) {
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		// 如果类型转换失败，返回默认值
		return utils.BuildSuccessResp(types.UserDetailData{
			Id:                    0,
			Username:              "",
			RewardAmount:          "",
			NbaRewardAmount:       "",
			NbaLotteryRemainCount: 0,
		}, "查询成功"), nil
	}

	user, err := l.svcCtx.UserModel.FindOne(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户信息失败"), nil
	}

	totalReward, err := l.svcCtx.UserGuessRecordModel.GetUserTotalRewardAmount(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户奖励金额失败"), nil
	}

	nbaTotalReward, err := l.svcCtx.UserNabGuessRecordModel.GetUserTotalRewardAmount(l.ctx, userId)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询用户NBA奖励金额失败"), nil
	}

	userDetail := types.UserDetailData{
		Id:                    user.Id,
		Username:              user.Username,
		RewardAmount:          strconv.FormatFloat(totalReward, 'f', 2, 64),
		NbaRewardAmount:       strconv.FormatFloat(nbaTotalReward, 'f', 2, 64),
		NbaLotteryRemainCount: int64(user.NbaLotteryRemainCount),
	}

	return utils.BuildSuccessResp(userDetail, "查询成功"), nil
}
