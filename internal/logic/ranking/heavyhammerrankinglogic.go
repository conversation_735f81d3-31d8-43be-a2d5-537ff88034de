package ranking

import (
	"context"
	"fmt"

	"sport-server/internal/svc"
	"sport-server/internal/types"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type HeavyHammerRankingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHeavyHammerRankingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HeavyHammerRankingLogic {
	return &HeavyHammerRankingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HeavyHammerRankingLogic) HeavyHammerRanking(req *types.HeavyHammerRankingReq) (resp *types.UniversalResponse, err error) {
	if req.StartTime != "" {
		req.StartTime = req.StartTime + " 00:00:00"
	}
	if req.EndTime != "" {
		req.EndTime = req.EndTime + " 23:59:59"
	}

	// 查询重锤命中率排行榜数据
	rankings, err := l.svcCtx.UserGuessRecordModel.GetHeavyHammerRanking(l.ctx, req.StartTime, req.EndTime)
	if err != nil {
		return utils.BuildErrorRespWithLog(err, l, "查询重锤命中率排行榜失败"), nil
	}

	// 提取所有用户ID
	var userIds []int64
	for _, ranking := range rankings {
		userIds = append(userIds, ranking.UserId)
	}

	// 批量查询用户信息
	userMap := make(map[int64]string)
	if len(userIds) > 0 {
		users, err := l.svcCtx.UserModel.FindByIds(l.ctx, userIds)
		if err != nil {
			l.Errorf("批量查询用户信息失败: %v", err)
		} else {
			// 构建用户ID到昵称的映射
			for _, user := range users {
				userMap[user.Id] = user.Username
			}
		}
	}

	// 构建响应数据
	var rankingItems []types.HeavyHammerRankingItem
	lastHitRate := ""
	lastRank := 0
	for i, ranking := range rankings {
		// 获取用户昵称
		nickname, exists := userMap[ranking.UserId]
		if !exists {
			nickname = fmt.Sprintf("用户%d", ranking.UserId) // 使用默认昵称
		}

		// 计算命中率
		var hitRate string
		if ranking.TotalCount > 0 {
			rate := float64(ranking.HitCount) / float64(ranking.TotalCount) * 100
			hitRate = fmt.Sprintf("%.0f%%", rate)
		} else {
			hitRate = "0%"
		}
		var Rank int
		if hitRate == lastHitRate {
			Rank = lastRank
		} else {
			Rank = i + 1
		}
		rankingItems = append(rankingItems, types.HeavyHammerRankingItem{
			Rank:       Rank,
			UserName:   nickname,
			HitCount:   ranking.HitCount,
			TotalCount: ranking.TotalCount,
			HitRate:    hitRate,
		})
		lastRank = Rank
		lastHitRate = hitRate
	}

	return utils.BuildSuccessResp(rankingItems, "查询成功"), nil
}
