// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type BaseResponse struct {
	Code     int    `json:"code"`
	Message  string `json:"message"`
	ErrorMsg string `json:"error_msg"`
}

type DefaultRoundItem struct {
	CompShortZh string `json:"comp_short_zh"`
	RoundNum    int    `json:"round_num"`
}

type DefaultRoundListReq struct {
}

type DefaultRoundListResp struct {
	BaseResponse
	Data []DefaultRoundItem `json:"data"`
}

type GetMatchListReq struct {
	CompShortZh string `form:"comp_short_zh" validate:"required"`
	RoundNum    int    `form:"round_num" validate:"required,min=1,max=38"`
}

type GetMatchListResp struct {
	BaseResponse
	Data MatchListData `json:"data"`
}

type GuessItem struct {
	MatchId int `json:"match_id" validate:"required"`
	Type    int `json:"type" validate:"required,oneof=1 2"`
	WhoWin  int `json:"who_win" validate:"required,oneof=1 2"`
}

type GuessResult struct {
	WhoWin int  `json:"who_win"`
	Result *int `json:"result,omitempty"`
}

type HeavyHammerRankingItem struct {
	Rank       int    `json:"rank"`
	UserName   string `json:"username"`
	HitCount   int    `json:"hit_count"`
	TotalCount int    `json:"total_count"`
	HitRate    string `json:"hit_rate"`
}

type HeavyHammerRankingReq struct {
	StartTime string `form:"start_time,optional"`
	EndTime   string `form:"end_time,optional"`
}

type HeavyHammerRankingResp struct {
	BaseResponse
	Data []HeavyHammerRankingItem `json:"data"`
}

type MatchInfo struct {
	MatchId        int    `json:"match_id"`
	MatchTime      string `json:"match_time"`
	Home           string `json:"home"`
	HomeLogo       string `json:"home_logo"`
	Away           string `json:"away"`
	AwayLogo       string `json:"away_logo"`
	HomeGoals      *int   `json:"home_goals"`
	AwayGoals      *int   `json:"away_goals"`
	HandicapStatus int    `json:"handicap_status"`
	CloseTime      string `json:"close_time"`
	ConcedeNum     string `json:"concede_num"`
	OverUnderNum   string `json:"over_under_num"`
	HandicapGuess  int    `json:"handicap_guess"`
	OverUnderGuess int    `json:"over_under_guess"`
}

type MatchListData struct {
	List                   []MatchInfo `json:"list"`
	UserGuessCount         int64       `json:"user_guess_count"`
	MinGuessCountForReward int         `json:"min_guess_count_for_reward"`
	RoundStatus            int         `json:"round_status"`
}

type MyGuessItem struct {
	Id               int64   `json:"id"`
	MatchId          int     `json:"match_id"`
	Home             string  `json:"home"`
	Away             string  `json:"away"`
	ConcedeNum       float64 `json:"concede_num"`
	OverUnderNum     float64 `json:"over_under_num"`
	WhoWin           int     `json:"who_win"`
	Result           int     `json:"result"`
	HomeLogo         string  `json:"home_logo"`
	AwayLogo         string  `json:"away_logo"`
	CompShortZh      string  `json:"comp_short_zh"`
	RoundNum         int     `json:"round_num"`
	IsHeavyHammer    int     `json:"is_heavy_hammer"`
	Type             int     `json:"type"`
	IsCanHeavyHammer int     `json:"is_can_heavy_hammer"`
	MatchTimeFormat  string  `json:"match_time_format"`
	HomeGoals        string  `json:"home_goals"`
	AwayGoals        string  `json:"away_goals"`
}

type MyGuessListReq struct {
	CompShortZh  string `form:"comp_short_zh,optional"`
	RoundNum     int    `form:"round_num,optional" validate:"min=1,max=38"`
	IsOpenResult int    `form:"is_open_result" validate:"required,oneof=1 2"`
}

type MyGuessListResp struct {
	BaseResponse
}

type NbaDrawCardReq struct {
}

type NbaDrawCardResp struct {
	UniversalResponse
}

type NbaExchangeCardGroupReq struct {
	NabCardGroupsId int `json:"nab_card_groups_id" validate:"required"`
}

type NbaExchangeCardGroupResp struct {
	UniversalResponse
}

type NbaGetCardGroupListReq struct {
}

type NbaGetCardGroupListResp struct {
	UniversalResponse
}

type NbaGetMatchListReq struct {
}

type NbaGetMatchListResp struct {
	BaseResponse
	Data NbaMatchListData `json:"data"`
}

type NbaGetStarListReq struct {
	Level string `form:"level,optional"`
}

type NbaGetStarListResp struct {
	UniversalResponse
}

type NbaGuessItem struct {
	MatchId int `json:"match_id" validate:"required"`
	Type    int `json:"type" validate:"required,oneof=1 2"`
	WhoWin  int `json:"who_win" validate:"required,oneof=1 2"`
}

type NbaMatchInfo struct {
	MatchId        int    `json:"match_id"`
	MatchTime      string `json:"match_time"`
	Home           string `json:"home"`
	HomeLogo       string `json:"home_logo"`
	Away           string `json:"away"`
	AwayLogo       string `json:"away_logo"`
	HomeGoals      *int   `json:"home_goals"`
	AwayGoals      *int   `json:"away_goals"`
	HandicapStatus int    `json:"handicap_status"`
	CloseTime      string `json:"close_time"`
	ConcedeNum     string `json:"concede_num"`
	OverUnderNum   string `json:"over_under_num"`
	HandicapGuess  int    `json:"handicap_guess"`
	OverUnderGuess int    `json:"over_under_guess"`
}

type NbaMatchListData struct {
	List           []NbaMatchInfo `json:"list"`
	UserGuessCount int64          `json:"user_guess_count"`
	Date           string         `json:"date"`
}

type NbaMyGuessItem struct {
	Id               int64   `json:"id"`
	MatchId          int     `json:"match_id"`
	Home             string  `json:"home"`
	Away             string  `json:"away"`
	ConcedeNum       float64 `json:"concede_num"`
	OverUnderNum     float64 `json:"over_under_num"`
	WhoWin           int     `json:"who_win"`
	Result           int     `json:"result"`
	HomeLogo         string  `json:"home_logo"`
	AwayLogo         string  `json:"away_logo"`
	IsHeavyHammer    int     `json:"is_heavy_hammer"`
	Type             int     `json:"type"`
	IsCanHeavyHammer int     `json:"is_can_heavy_hammer"`
	MatchTimeFormat  string  `json:"match_time_format"`
	HomeGoals        string  `json:"home_goals"`
	AwayGoals        string  `json:"away_goals"`
}

type NbaMyGuessListReq struct {
	IsOpenResult int    `form:"is_open_result" validate:"required,oneof=1 2"`
	Date         string `form:"date,optional"`
}

type NbaMyGuessListResp struct {
	UniversalResponse
}

type NbaSetHeavyHammerReq struct {
	Id int64 `json:"user_guess_record_id" validate:"required"`
}

type NbaSetHeavyHammerResp struct {
	UniversalResponse
}

type NbaSubmitGuessReq struct {
	Guesses []NbaGuessItem `json:"guesses" validate:"required,min=1"`
}

type NbaSubmitGuessResp struct {
	UniversalResponse
}

type ProfitRankingItem struct {
	Rank         int     `json:"rank"`
	UserName     string  `json:"username"`
	WinCount     int     `json:"win_count"`
	LossCount    int     `json:"loss_count"`
	ProfitAmount float64 `json:"profit_amount"`
}

type ProfitRankingReq struct {
	StartTime string `form:"start_time,optional"`
	EndTime   string `form:"end_time,optional"`
}

type ProfitRankingResp struct {
	BaseResponse
}

type SetHeavyHammerReq struct {
	UserGuessRecordId int64 `json:"user_guess_record_id" validate:"required"`
}

type SetHeavyHammerResp struct {
	BaseResponse
}

type SubmitGuessReq struct {
	Guesses []GuessItem `json:"guesses" validate:"required,min=1"`
}

type SubmitGuessResp struct {
	BaseResponse
}

type ThirdPartyLoginData struct {
	Url string `json:"url"`
}

type ThirdPartyLoginReq struct {
	UserId     int64  `json:"userId" validate:"required"`
	Name       string `json:"name" validate:"required"`
	PlatformId string `json:"platformId" validate:"required"`
}

type ThirdPartyLoginResp struct {
	BaseResponse
	Data ThirdPartyLoginData `json:"data"`
}

type UniversalResponse struct {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	ErrorMsg string      `json:"error_msg"`
	Data     interface{} `json:"data"`
}

type UserDetailData struct {
	Id                    int64  `json:"id"`
	Username              string `json:"username"`
	RewardAmount          string `json:"reward_amount"`
	NbaRewardAmount       string `json:"nba_reward_amount"`
	NbaLotteryRemainCount int64  `json:"nba_lottery_remain_count"`
	NbaLotteryTodayCount  int64  `json:"nba_lottery_today_count"`
}

type UserDetailReq struct {
}

type UserDetailResp struct {
	BaseResponse
	Data UserDetailData `json:"data"`
}

// GetAllResponseTypes 返回所有响应类型的实例
// 此函数由 tools/update_types.go 自动生成，请勿手动修改
func GetAllResponseTypes() []interface{} {
	return []interface{}{
		DefaultRoundListResp{},
		GetMatchListResp{},
		HeavyHammerRankingResp{},
		MyGuessListResp{},
		NbaDrawCardResp{},
		NbaExchangeCardGroupResp{},
		NbaGetCardGroupListResp{},
		NbaGetMatchListResp{},
		NbaGetStarListResp{},
		NbaMyGuessListResp{},
		NbaSetHeavyHammerResp{},
		NbaSubmitGuessResp{},
		ProfitRankingResp{},
		SetHeavyHammerResp{},
		SubmitGuessResp{},
		ThirdPartyLoginResp{},
		UserDetailResp{},
	}
}

// GetResponseTypeNames 返回所有响应类型的名称
// 此函数由 tools/update_types.go 自动生成，请勿手动修改
func GetResponseTypeNames() []string {
	return []string{
		"DefaultRoundListResp",
		"GetMatchListResp",
		"HeavyHammerRankingResp",
		"MyGuessListResp",
		"NbaDrawCardResp",
		"NbaExchangeCardGroupResp",
		"NbaGetCardGroupListResp",
		"NbaGetMatchListResp",
		"NbaGetStarListResp",
		"NbaMyGuessListResp",
		"NbaSetHeavyHammerResp",
		"NbaSubmitGuessResp",
		"ProfitRankingResp",
		"SetHeavyHammerResp",
		"SubmitGuessResp",
		"ThirdPartyLoginResp",
		"UserDetailResp",
	}
}
