package nab

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/logic/nab"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

// 提交NBA竞猜
func NbaSubmitGuessHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.NbaSubmitGuessReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := nab.NewNbaSubmitGuessLogic(r.Context(), svcCtx)
		resp, err := l.NbaSubmitGuess(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
