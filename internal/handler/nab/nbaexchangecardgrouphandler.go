package nab

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/logic/nab"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

// NBA卡组兑换
func NbaExchangeCardGroupHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.NbaExchangeCardGroupReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := nab.NewNbaExchangeCardGroupLogic(r.Context(), svcCtx)
		resp, err := l.NbaExchangeCardGroup(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
