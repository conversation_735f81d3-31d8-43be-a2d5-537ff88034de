package nab

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/logic/nab"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

// 我的NBA竞猜列表
func NbaMyGuessListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.NbaMyGuessListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := nab.NewNbaMyGuessListLogic(r.Context(), svcCtx)
		resp, err := l.NbaMyGuessList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
