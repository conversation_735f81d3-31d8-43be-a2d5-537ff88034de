// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	account "sport-server/internal/handler/account"
	guess "sport-server/internal/handler/guess"
	match "sport-server/internal/handler/match"
	nab "sport-server/internal/handler/nab"
	ranking "sport-server/internal/handler/ranking"
	"sport-server/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 第三方登录
				Method:  http.MethodPost,
				Path:    "/user/third-party-login",
				Handler: account.ThirdpartyloginHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 用户信息
					Method:  http.MethodPost,
					Path:    "/user/detail",
					Handler: account.DetailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 我的竞猜列表
					Method:  http.MethodGet,
					Path:    "/guess/my-list",
					Handler: guess.MyGuessListHandler(serverCtx),
				},
				{
					// 设为重锤单
					Method:  http.MethodPost,
					Path:    "/guess/set-heavy-hammer",
					Handler: guess.SetHeavyHammerHandler(serverCtx),
				},
				{
					// 提交竞猜
					Method:  http.MethodPost,
					Path:    "/guess/submit",
					Handler: guess.SubmitGuessHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 获取赛程列表
					Method:  http.MethodGet,
					Path:    "/match/list",
					Handler: match.GetMatchListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取各赛事默认轮次
				Method:  http.MethodPost,
				Path:    "/match/default-round-list",
				Handler: match.DefaultRoundListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// NBA卡组兑换
					Method:  http.MethodPost,
					Path:    "/card-group/exchange",
					Handler: nab.NbaExchangeCardGroupHandler(serverCtx),
				},
				{
					// 获取NBA卡组列表
					Method:  http.MethodGet,
					Path:    "/card-group/list",
					Handler: nab.NbaGetCardGroupListHandler(serverCtx),
				},
				{
					// NBA抽卡
					Method:  http.MethodPost,
					Path:    "/draw-card",
					Handler: nab.NbaDrawCardHandler(serverCtx),
				},
				{
					// 我的竞猜列表
					Method:  http.MethodGet,
					Path:    "/guess/my-list",
					Handler: nab.NbaMyGuessListHandler(serverCtx),
				},
				{
					// 设为重锤单
					Method:  http.MethodPost,
					Path:    "/guess/set-heavy-hammer",
					Handler: nab.NbaSetHeavyHammerHandler(serverCtx),
				},
				{
					// 提交竞猜
					Method:  http.MethodPost,
					Path:    "/guess/submit",
					Handler: nab.NbaSubmitGuessHandler(serverCtx),
				},
				{
					// 获取NBA赛程列表
					Method:  http.MethodGet,
					Path:    "/match/list",
					Handler: nab.NabmatchlistHandler(serverCtx),
				},
				{
					// 获取NBA球星列表
					Method:  http.MethodGet,
					Path:    "/star/list",
					Handler: nab.NbaGetStarListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/nba"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 重锤命中率排行榜
				Method:  http.MethodGet,
				Path:    "/ranking/heavy-hammer",
				Handler: ranking.HeavyHammerRankingHandler(serverCtx),
			},
			{
				// 盈利排行榜
				Method:  http.MethodGet,
				Path:    "/ranking/profit",
				Handler: ranking.ProfitRankingHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1"),
	)
}
