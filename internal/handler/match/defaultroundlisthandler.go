package match

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/logic/match"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

// 获取各赛事默认轮次
func DefaultRoundListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DefaultRoundListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := match.NewDefaultRoundListLogic(r.Context(), svcCtx)
		resp, err := l.DefaultRoundList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
