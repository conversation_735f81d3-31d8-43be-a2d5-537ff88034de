package handler

import (
	"encoding/json"
	"github.com/zeromicro/go-zero/rest"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// SwaggerHandler 处理swagger文档访问，动态生成swagger文档
func SwaggerHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取当前工作目录
		wd, err := os.Getwd()
		if err != nil {
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		// 构建文件路径
		apiPath := filepath.Join(wd, "api", "sport.api")
		swaggerPath := filepath.Join(wd, "swagger.json")

		// 检查API文件是否存在
		if _, err := os.Stat(apiPath); os.IsNotExist(err) {
			http.Error(w, "API file not found", http.StatusNotFound)
			return
		}

		// 直接重新生成swagger.json
		//if err := generateSwaggerDoc(wd); err != nil {
		//	// 如果生成失败，尝试读取现有文件
		//	if _, readErr := os.Stat(swaggerPath); readErr != nil {
		//		http.Error(w, "Failed to generate swagger doc and no existing file", http.StatusInternalServerError)
		//		return
		//	}
		//}

		// 读取swagger.json文件
		content, err := os.ReadFile(swaggerPath)
		if err != nil {
			http.Error(w, "Error reading swagger file", http.StatusInternalServerError)
			return
		}

		// 动态替换host为当前请求的host
		var swaggerDoc map[string]interface{}
		if err := json.Unmarshal(content, &swaggerDoc); err == nil {
			// 设置当前请求的host
			swaggerDoc["host"] = r.Host
			
			// 根据请求协议动态设置schemes
			scheme := "http"
			if r.TLS != nil || r.Header.Get("X-Forwarded-Proto") == "https" || strings.HasPrefix(r.Host, "https://") {
				scheme = "https"
			}
			swaggerDoc["schemes"] = []string{scheme}

			// 重新序列化
			if modifiedContent, err := json.Marshal(swaggerDoc); err == nil {
				content = modifiedContent
			}
		}

		// 设置响应头
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// 返回swagger.json内容
		w.Write(content)
	}
}

// getTagFromPath 根据路径自动生成tag分组
func getTagFromPath(path string) string {
	parts := strings.Split(path, "/")
	if len(parts) >= 4 && parts[1] == "api" && parts[2] == "v1" {
		return parts[3] // 返回第三级路径作为tag
	}
	return "default"
}

// SwaggerUIHandler 处理swagger UI页面
func SwaggerUIHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Swagger UI HTML页面
		html := `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/swagger/doc.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>`

		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write([]byte(html))
	}
}

// RegisterSwaggerRoutes 注册swagger相关路由
func RegisterSwaggerRoutes(server *rest.Server) {
	server.AddRoutes([]rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/swagger/index.html",
			Handler: SwaggerUIHandler(),
		},
		{
			Method:  http.MethodGet,
			Path:    "/swagger/doc.json",
			Handler: SwaggerHandler(),
		},
	})
}
