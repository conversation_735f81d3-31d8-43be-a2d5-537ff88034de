package ranking

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/logic/ranking"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

func ProfitRankingHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ProfitRankingReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := ranking.NewProfitRankingLogic(r.Context(), svcCtx)
		resp, err := l.ProfitRanking(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
