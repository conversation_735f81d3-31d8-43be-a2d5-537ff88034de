package guess

import (
	"net/http"
	"sport-server/internal/logic/guess"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

func SetHeavyHammerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetHeavyHammerReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := guess.NewSetHeavyHammerLogic(r.Context(), svcCtx)
		resp, err := l.<PERSON>eavy<PERSON>ammer(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
