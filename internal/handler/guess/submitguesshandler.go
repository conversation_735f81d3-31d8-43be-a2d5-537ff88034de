package guess

import (
	"fmt"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"sport-server/internal/constants"
	"sport-server/internal/logic/guess"
	"sport-server/internal/svc"
	"sport-server/internal/types"
)

func SubmitGuessHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SubmitGuessReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		// 验证参数
		for _, guess := range req.Guesses {
			// 验证竞猜类型
			if !constants.ValidGuessTypes[guess.Type] {
				httpx.WriteJson(w, 400, map[string]interface{}{
					"code":    400,
					"message": fmt.Sprintf("无效的竞猜类型: %d", guess.Type),
				})
				return
			}

			// 验证猜谁胜
			if !constants.ValidWhoWinTypes[guess.WhoWin] {
				httpx.WriteJson(w, 400, map[string]interface{}{
					"code":    400,
					"message": fmt.Sprintf("无效的胜负选择: %d", guess.WhoWin),
				})
				return
			}
		}

		l := guess.NewSubmitGuessLogic(r.Context(), svcCtx)
		resp, err := l.SubmitGuess(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
