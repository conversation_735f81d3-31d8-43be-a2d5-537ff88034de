package scheduler

import (
	"context"
	"fmt"

	"sport-server/internal/cron/jobs"

	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"
)

// Scheduler 定时任务调度器
type Scheduler struct {
	cron *cron.Cron
	jobs map[string]jobs.Job
}

// NewScheduler 创建调度器
func NewScheduler() *Scheduler {
	return &Scheduler{
		cron: cron.New(cron.WithSeconds()), // 支持秒级调度
		jobs: make(map[string]jobs.Job),
	}
}

// RegisterJob 注册任务
func (s *Scheduler) RegisterJob(job jobs.Job) error {
	if !job.Enabled() {
		logx.Infof("任务 %s 已禁用，跳过注册", job.Name())
		return nil
	}

	// 包装任务执行函数，添加日志和错误处理
	wrappedFunc := func() {
		ctx := context.Background()
		logx.Infof("开始执行任务: %s", job.Name())
		
		if err := job.Execute(ctx); err != nil {
			logx.Errorf("任务 %s 执行失败: %v", job.Name(), err)
		} else {
			logx.Infof("任务 %s 执行完成", job.Name())
		}
	}

	// 添加到cron调度器
	_, err := s.cron.AddFunc(job.Schedule(), wrappedFunc)
	if err != nil {
		return fmt.Errorf("注册任务 %s 失败: %v", job.Name(), err)
	}

	s.jobs[job.Name()] = job
	logx.Infof("任务 %s 注册成功，调度规则: %s", job.Name(), job.Schedule())
	return nil
}

// Start 启动调度器
func (s *Scheduler) Start() {
	logx.Info("定时任务调度器启动")
	s.cron.Start()
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	logx.Info("定时任务调度器停止")
	s.cron.Stop()
}

// GetJobs 获取所有已注册的任务
func (s *Scheduler) GetJobs() map[string]jobs.Job {
	return s.jobs
}
