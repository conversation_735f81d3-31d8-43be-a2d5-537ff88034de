package jobs

import (
	"context"
	"database/sql"
	"fmt"

	"sport-server/internal/constants"

	"github.com/zeromicro/go-zero/core/logx"
)

// HandicapStatusJob 封盘状态任务
type HandicapStatusJob struct {
	db *sql.DB
}

// NewHandicapStatusJob 创建封盘状态任务
func NewHandicapStatusJob(db *sql.DB) *HandicapStatusJob {
	return &HandicapStatusJob{
		db: db,
	}
}

// Name 任务名称
func (j *HandicapStatusJob) Name() string {
	return "HandicapStatus"
}

// Execute 执行任务
func (j *HandicapStatusJob) Execute(ctx context.Context) error {
	// 执行SQL：把match_time+close_time_seconds小于等于当前时间时，修改handicap_status从开盘中改为已截止
	query := fmt.Sprintf(`
		UPDATE db_football_match
		SET handicap_status = %d
		WHERE handicap_status = %d
		AND (match_time + IFNULL(close_time_seconds, 0)) <= UNIX_TIMESTAMP()
	`, constants.HandicapStatusClosed, constants.HandicapStatusOpen)

	result, err := j.db.ExecContext(ctx, query)
	if err != nil {
		logx.Errorf("db_football_match执行封盘状态更新失败: %v", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logx.Errorf("db_football_match获取影响行数失败: %v", err)
		return err
	}

	if rowsAffected > 0 {
		logx.Infof("db_football_match封盘状态任务执行成功，更新了 %d 条记录", rowsAffected)
	}

	query = fmt.Sprintf(`
		UPDATE db_matches_schedulev2
		SET handicap_status = %d
		WHERE handicap_status = %d
		AND (match_time + IFNULL(close_time_seconds, 0)) <= UNIX_TIMESTAMP()
	`, constants.HandicapStatusClosed, constants.HandicapStatusOpen)

	result, err = j.db.ExecContext(ctx, query)
	if err != nil {
		logx.Errorf("db_matches_schedulev2执行封盘状态更新失败: %v", err)
		return err
	}

	rowsAffected, err = result.RowsAffected()
	if err != nil {
		logx.Errorf("db_matches_schedulev2获取影响行数失败: %v", err)
		return err
	}

	if rowsAffected > 0 {
		logx.Infof("db_matches_schedulev2封盘状态任务执行成功，更新了 %d 条记录", rowsAffected)
	}

	return nil
}

// Schedule 返回cron表达式 - 每分钟执行一次
func (j *HandicapStatusJob) Schedule() string {
	return "0 */1 * * * *" // 每分钟的第0秒执行
}

// Enabled 是否启用
func (j *HandicapStatusJob) Enabled() bool {
	return true
}
