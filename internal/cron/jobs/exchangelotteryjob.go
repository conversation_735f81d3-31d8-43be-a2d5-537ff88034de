package jobs

import (
	"context"
	"fmt"
	"time"

	"sport-server/internal/constants"
	"sport-server/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// ExchangeLotteryJob 兑换抽奖次数任务
type ExchangeLotteryJob struct {
	svcCtx *svc.ServiceContext
}

func NewExchangeLotteryJob(svcCtx *svc.ServiceContext) *ExchangeLotteryJob {
	return &ExchangeLotteryJob{
		svcCtx: svcCtx,
	}
}

// Name 任务名称
func (j *ExchangeLotteryJob) Name() string {
	return "ExchangeLottery"
}

// Execute 执行任务
func (j *ExchangeLotteryJob) Execute(ctx context.Context) error {
	currentDate := time.Now().Format("2006-01-02")

	query := `
		SELECT user_id, SUM(reward_amount) as sum_reward_amount
		FROM db_user_nabguess_record 
		WHERE is_exchanged = ?
		GROUP BY user_id
		HAVING sum_reward_amount >= 1
	`

	type UserReward struct {
		UserId          int64   `db:"user_id"`
		SumRewardAmount float64 `db:"sum_reward_amount"`
	}

	var userRewards []UserReward
	err := j.svcCtx.GetConn().QueryRowsCtx(ctx, &userRewards, query, constants.ExchangeStatusNotExchanged)
	if err != nil {
		logx.Errorf("查询用户奖励金额失败: %v", err)
		return err
	}

	if len(userRewards) == 0 {
		logx.Info("没有需要兑换的用户")
		return nil
	}

	successCount := 0
	for _, userReward := range userRewards {
		err := j.executeUserExchangeTransaction(ctx, userReward.UserId, userReward.SumRewardAmount, currentDate)
		if err != nil {
			logx.Errorf("用户 %d 兑换事务执行失败: %v", userReward.UserId, err)
			continue
		}
		successCount++
	}

	logx.Infof("兑换抽奖次数任务执行完成，成功处理 %d 个用户，共 %d 个用户", successCount, len(userRewards))
	return nil
}

func (j *ExchangeLotteryJob) executeUserExchangeTransaction(ctx context.Context, userId int64, sumRewardAmount float64, currentDate string) error {
	exchangeCount := int(sumRewardAmount)

	return j.svcCtx.GetConn().TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		_, err := session.ExecCtx(ctx,
			"UPDATE db_user SET nba_lottery_remain_count = nba_lottery_remain_count + ?, update_time = NOW() WHERE id = ?",
			exchangeCount, userId)
		if err != nil {
			return fmt.Errorf("更新用户抽奖次数失败: %v", err)
		}

		_, err = session.ExecCtx(ctx, `
			INSERT INTO db_user_nab_date (date, user_id, lottery_count, exchange_count, exchange_limit_count, create_time, update_time)
			VALUES (?, ?, 0, ?, 0, NOW(), NOW())
			ON DUPLICATE KEY UPDATE 
				exchange_count = exchange_count + ?,
				update_time = NOW()
		`, currentDate, userId, exchangeCount, exchangeCount)
		if err != nil {
			return fmt.Errorf("更新用户日期兑换记录失败: %v", err)
		}

		_, err = session.ExecCtx(ctx,
			"UPDATE db_user_nabguess_record SET is_exchanged = ? WHERE user_id = ? AND is_exchanged = ?",
			constants.ExchangeStatusExchanged, userId, constants.ExchangeStatusNotExchanged)
		if err != nil {
			return fmt.Errorf("更新竞猜记录兑换状态失败: %v", err)
		}

		logx.Infof("用户 %d 兑换成功，增加 %d 次抽奖机会", userId, exchangeCount)
		return nil
	})
}

// Schedule 返回cron表达式 - 每天下午5点执行
func (j *ExchangeLotteryJob) Schedule() string {
	return "0 0 17 * * *" // 每天17:00:00执行
}

// Enabled 是否启用
func (j *ExchangeLotteryJob) Enabled() bool {
	return true
}
