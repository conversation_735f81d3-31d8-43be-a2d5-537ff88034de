package middleware

import (
	"bytes"
	"io"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type RequestLogMiddleware struct {
}

func NewRequestLogMiddleware() *RequestLogMiddleware {
	return &RequestLogMiddleware{}
}

func (m *RequestLogMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// 读取请求体
		var bodyBytes []byte
		if r.Body != nil {
			bodyBytes, _ = io.ReadAll(r.Body)
			r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}

		// 记录请求日志
		logx.Infof("Request: %s %s, Body: %s, RemoteAddr: %s, UserAgent: %s",
			r.Method, r.URL.Path, string(bodyBytes), r.<PERSON>, r.UserAgent())

		// 创建响应记录器
		recorder := &responseRecorder{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
			body:           bytes.NewBuffer(nil),
		}

		// 执行下一个处理器
		next(recorder, r)

		// 记录响应日志
		duration := time.Since(start)
		logx.Infof("Response: %s %s, Status: %d, Duration: %v, Response: %s",
			r.Method, r.URL.Path, recorder.statusCode, duration, recorder.body.String())
	}
}

// responseRecorder 用于记录响应内容
type responseRecorder struct {
	http.ResponseWriter
	statusCode int
	body       *bytes.Buffer
}

func (r *responseRecorder) WriteHeader(statusCode int) {
	r.statusCode = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

func (r *responseRecorder) Write(data []byte) (int, error) {
	r.body.Write(data)
	return r.ResponseWriter.Write(data)
}
