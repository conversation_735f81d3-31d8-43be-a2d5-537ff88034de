package middleware

import (
	"fmt"
	"net/http"
)

type CorsMiddleware struct {
}

func NewCorsMiddleware() *CorsMiddleware {
	return &CorsMiddleware{}
}

func (m *CorsMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("=== CORS Middleware ===\n")
		fmt.Printf("Method: %s, Path: %s\n", r.Method, r.URL.Path)
		fmt.Printf("Origin: %s\n", r.Header.Get("Origin"))
		
		// 直接在原始ResponseWriter上设置CORS头部
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON>er().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH")
		w.Header().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Requested-With, Device-Info, device-info, Platform, platform, Referer, referer, Reg-Source, reg-source, User-Agent, user-agent, Origin, origin")
		w.Header().Set("Access-Control-Max-Age", "86400")
		w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type")

		fmt.Printf("CORS headers set\n")
		
		// 处理预检请求
		if r.Method == "OPTIONS" {
			fmt.Printf("Handling OPTIONS request\n")
			w.WriteHeader(http.StatusOK)
			fmt.Printf("=== End CORS Middleware ===\n")
			return
		}

		fmt.Printf("Continuing to next handler\n")
		fmt.Printf("=== End CORS Middleware ===\n")
		
		// 继续处理请求
		next(w, r)
	}
}
