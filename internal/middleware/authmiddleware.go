package middleware

import (
	"context"
	"net/http"
	"strings"

	"sport-server/internal/config"
	"sport-server/internal/utils"

	"github.com/zeromicro/go-zero/rest/httpx"
)

type AuthMiddleware struct {
	config config.Config
}

func NewAuthMiddleware(config config.Config) *AuthMiddleware {
	return &AuthMiddleware{
		config: config,
	}
}

func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {

		if r.URL.Path == "/api/v1/match/list" && r.Header.Get("Authorization") == "" {
			next(w, r)
			return
		}

		var tokenString string

		// 优先从Authorization header获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			tokenString = strings.TrimPrefix(authHeader, "Bearer ")
		} else {
			// 如果没有Authorization header，尝试从Cookie获取token（此写法仅开发调试方便）
			cookie, err := r.<PERSON>("sport_token")
			if err != nil || cookie.Value == "" {
				httpx.WriteJson(w, 401, map[string]interface{}{
					"code":      401,
					"message":   "缺少认证信息",
					"error_msg": "",
					"data":      []interface{}{},
				})
				return
			}
			tokenString = cookie.Value
		}

		// 使用JWT工具类解析token
		claims, err := utils.ParseToken(tokenString, m.config.Auth.AccessSecret)
		if err != nil {
			httpx.WriteJson(w, 401, map[string]interface{}{
				"code":      401,
				"message":   "无效的token",
				"error_msg": "",
				"data":      []interface{}{},
			})
			return
		}

		// 将用户信息添加到context中
		ctx := context.WithValue(r.Context(), "userId", claims.UserId)
		ctx = context.WithValue(ctx, "nickname", claims.Nickname)
		r = r.WithContext(ctx)

		// 继续处理请求
		next(w, r)
	}
}
