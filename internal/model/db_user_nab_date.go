package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type UserNabDate struct {
	Id                 int64     `db:"id"`
	Date               time.Time `db:"date"`
	UserId             int64     `db:"user_id"`
	LotteryCount       int       `db:"lottery_count"`
	ExchangeCount      int       `db:"exchange_count"`
	ExchangeLimitCount int       `db:"exchange_limit_count"`
	CreateTime         time.Time `db:"create_time"`
	UpdateTime         time.Time `db:"update_time"`
}

type UserNabDateModel interface {
	Insert(ctx context.Context, data *UserNabDate) (sql.Result, error)
	FindOne(ctx context.Context, id int64) (*UserNabDate, error)
	FindByUserAndDate(ctx context.Context, userId int64, date string) (*UserNabDate, error)
	Update(ctx context.Context, data *UserNabDate) error
	IncrementLotteryCount(ctx context.Context, userId int64, date string) error
}

type defaultUserNabDateModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserNabDateModel(conn sqlx.SqlConn) UserNabDateModel {
	return &defaultUserNabDateModel{
		conn:  conn,
		table: "`db_user_nab_date`",
	}
}

func (m *defaultUserNabDateModel) Insert(ctx context.Context, data *UserNabDate) (sql.Result, error) {
	query := `insert into ` + m.table + ` (date, user_id, lottery_count, exchange_count, exchange_limit_count) values (?, ?, ?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.Date, data.UserId, data.LotteryCount, data.ExchangeCount, data.ExchangeLimitCount)
}

func (m *defaultUserNabDateModel) FindOne(ctx context.Context, id int64) (*UserNabDate, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "date", "user_id", "lottery_count", "exchange_count", "exchange_limit_count", "create_time", "update_time").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp UserNabDate
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserNabDateModel) FindByUserAndDate(ctx context.Context, userId int64, date string) (*UserNabDate, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "date", "user_id", "lottery_count", "exchange_count", "exchange_limit_count", "create_time", "update_time").
		From(m.table).
		Where("user_id = ?", userId).
		Where("date = ?", date).
		Limit(1)

	var resp UserNabDate
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserNabDateModel) Update(ctx context.Context, data *UserNabDate) error {
	query := `update ` + m.table + ` set lottery_count = ?, exchange_count = ?, exchange_limit_count = ?, update_time = ? where id = ?`
	_, err := m.conn.ExecCtx(ctx, query, data.LotteryCount, data.ExchangeCount, data.ExchangeLimitCount, data.UpdateTime, data.Id)
	return err
}

func (m *defaultUserNabDateModel) IncrementLotteryCount(ctx context.Context, userId int64, date string) error {
	query := `update ` + m.table + ` set lottery_count = lottery_count + 1, update_time = NOW() where user_id = ? and date = ?`
	_, err := m.conn.ExecCtx(ctx, query, userId, date)
	return err
}