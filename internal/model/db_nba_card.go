package model

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type NbaCard struct {
	Id         int       `db:"id"`
	NameZh     string    `db:"name_zh"`
	ImgUrl     string    `db:"img_url"`
	Score      string    `db:"score"`
	Level      string    `db:"level"`
	TeamName   string    `db:"team_name"`
	Location   string    `db:"location"`
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}

type NbaCardModel interface {
	FindOne(ctx context.Context, id int64) (*NbaCard, error)
	FindByLevel(ctx context.Context, level string) ([]*NbaCard, error)
	FindByIds(ctx context.Context, ids []int64) ([]*NbaCard, error)
	FindAll(ctx context.Context) ([]*NbaCard, error)
}

type defaultNbaCardModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewNbaCardModel(conn sqlx.SqlConn) NbaCardModel {
	return &defaultNbaCardModel{
		conn:  conn,
		table: "`db_nba_card`",
	}
}

func (m *defaultNbaCardModel) FindOne(ctx context.Context, id int64) (*NbaCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "name_zh", "img_url", "score", "level", "team_name", "location", "create_time", "update_time").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp NbaCard
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNbaCardModel) FindByLevel(ctx context.Context, level string) ([]*NbaCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "name_zh", "img_url", "score", "level", "team_name", "location", "create_time", "update_time").
		From(m.table).
		Where("level = ?", level)

	var cards []*NbaCard
	err := builder.QueryRows(ctx, m.conn, &cards)
	return cards, err
}

func (m *defaultNbaCardModel) FindByIds(ctx context.Context, ids []int64) ([]*NbaCard, error) {
	if len(ids) == 0 {
		return []*NbaCard{}, nil
	}

	// 将 []int64 转换为 []interface{}
	interfaceIds := make([]interface{}, len(ids))
	for i, id := range ids {
		interfaceIds[i] = id
	}

	builder := utils.NewSQLBuilder().
		Select("id", "name_zh", "img_url", "score", "level", "team_name", "location", "create_time", "update_time").
		From(m.table).
		WhereIn("id", interfaceIds)

	var cards []*NbaCard
	err := builder.QueryRows(ctx, m.conn, &cards)
	return cards, err
}

func (m *defaultNbaCardModel) FindAll(ctx context.Context) ([]*NbaCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "name_zh", "img_url", "score", "level", "team_name", "location", "create_time", "update_time").
		From(m.table)

	var cards []*NbaCard
	err := builder.QueryRows(ctx, m.conn, &cards)
	return cards, err
}