package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type UserNabGuessRecord struct {
	Id            int64     `db:"id"`
	UserId        int64     `db:"user_id"`
	MatchId       int       `db:"match_id"`
	Type          int       `db:"type"`
	RewardAmount  float64   `db:"reward_amount"`
	IsHeavyHammer int       `db:"is_heavy_hammer"`
	WhoWin        int       `db:"who_win"`
	Result        int       `db:"result"`
	IsExchanged   int       `db:"is_exchanged"`
	CreateTime    time.Time `db:"create_time"`
	UpdateTime    time.Time `db:"update_time"`
}

type UserNabGuessRecordModel interface {
	Insert(ctx context.Context, data *UserNabGuessRecord) (sql.Result, error)
	BatchInsert(ctx context.Context, data []*UserNabGuessRecord) error
	FindOne(ctx context.Context, id int64) (*UserNabGuessRecord, error)
	Update(ctx context.Context, data *UserNabGuessRecord) error
	CountUserGuessInDateRange(ctx context.Context, userId int64, startTime, endTime int64) (int64, error)
	FindByUserAndMatchIds(ctx context.Context, userId int64, matchIds []int) ([]*UserNabGuessRecord, error)
	CheckExistingRecords(ctx context.Context, userId int64, matchIds []int) (map[int]bool, error)
	FindMyGuessList(ctx context.Context, userId int64, startTime, endTime *int64, isOpenResult int) ([]*UserNabGuessRecord, error)
	GetUserTotalRewardAmount(ctx context.Context, userId int64) (float64, error)
}

type defaultUserNabGuessRecordModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserNabGuessRecordModel(conn sqlx.SqlConn) UserNabGuessRecordModel {
	return &defaultUserNabGuessRecordModel{
		conn:  conn,
		table: "`db_user_nabguess_record`",
	}
}

func (m *defaultUserNabGuessRecordModel) Insert(ctx context.Context, data *UserNabGuessRecord) (sql.Result, error) {
	query := `insert into ` + m.table + ` (user_id, match_id, type, reward_amount, is_heavy_hammer, who_win, is_exchanged) values (?, ?, ?, ?, ?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.UserId, data.MatchId, data.Type, data.RewardAmount, data.IsHeavyHammer, data.WhoWin, data.IsExchanged)
}

func (m *defaultUserNabGuessRecordModel) BatchInsert(ctx context.Context, data []*UserNabGuessRecord) error {
	if len(data) == 0 {
		return nil
	}

	query := `insert into ` + m.table + ` (user_id, match_id, type, reward_amount, is_heavy_hammer, who_win, is_exchanged) values `
	values := make([]interface{}, 0, len(data)*6)

	for i, record := range data {
		if i > 0 {
			query += ", "
		}
		query += "(?, ?, ?, ?, ?, ?, ?)"

		values = append(values, record.UserId, record.MatchId, record.Type, record.RewardAmount, record.IsHeavyHammer, record.WhoWin, record.IsExchanged)
	}

	_, err := m.conn.ExecCtx(ctx, query, values...)
	return err
}

func (m *defaultUserNabGuessRecordModel) FindOne(ctx context.Context, id int64) (*UserNabGuessRecord, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "is_exchanged", "create_time", "update_time").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp UserNabGuessRecord
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserNabGuessRecordModel) Update(ctx context.Context, data *UserNabGuessRecord) error {
	query := `update ` + m.table + ` set user_id = ?, match_id = ?, type = ?, reward_amount = ?, is_heavy_hammer = ?, who_win = ?, result = ?, is_exchanged = ?, update_time = ? where id = ?`
	_, err := m.conn.ExecCtx(ctx, query, data.UserId, data.MatchId, data.Type, data.RewardAmount, data.IsHeavyHammer, data.WhoWin, data.Result, data.IsExchanged, data.UpdateTime, data.Id)
	return err
}

func (m *defaultUserNabGuessRecordModel) CountUserGuessInDateRange(ctx context.Context, userId int64, startTime, endTime int64) (int64, error) {
	builder := utils.NewSQLBuilder().
		Select("COUNT(*)").
		From(m.table+" ugr").
		Join("db_matches_schedulev2 ms", "ugr.match_id = ms.id").
		Where("ugr.user_id = ?", userId).
		Where("ms.match_time >= ?", startTime).
		Where("ms.match_time <= ?", endTime)

	query, args := builder.Build()

	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, args...)
	return count, err
}

func (m *defaultUserNabGuessRecordModel) FindByUserAndMatchIds(ctx context.Context, userId int64, matchIds []int) ([]*UserNabGuessRecord, error) {
	if len(matchIds) == 0 {
		return []*UserNabGuessRecord{}, nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "is_exchanged", "create_time", "update_time").
		From(m.table).
		Where("user_id = ?", userId).
		WhereIn("match_id", values)

	var records []*UserNabGuessRecord
	err := builder.QueryRows(ctx, m.conn, &records)
	return records, err
}

func (m *defaultUserNabGuessRecordModel) CheckExistingRecords(ctx context.Context, userId int64, matchIds []int) (map[int]bool, error) {
	if len(matchIds) == 0 {
		return make(map[int]bool), nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	builder := utils.NewSQLBuilder().
		Select("match_id", "type").
		From(m.table).
		Where("user_id = ?", userId).
		WhereIn("match_id", values)

	type ExistingRecord struct {
		MatchId int `db:"match_id"`
		Type    int `db:"type"`
	}

	var existingRecords []*ExistingRecord
	err := builder.QueryRows(ctx, m.conn, &existingRecords)
	if err != nil {
		return nil, err
	}

	// 构建存在记录的map，使用match_id_type作为key
	existingMap := make(map[int]bool)
	for _, existing := range existingRecords {
		existingMap[existing.MatchId] = true
	}

	return existingMap, nil
}

func (m *defaultUserNabGuessRecordModel) FindMyGuessList(ctx context.Context, userId int64, startTime, endTime *int64, isOpenResult int) ([]*UserNabGuessRecord, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "is_exchanged", "create_time", "update_time").
		From(m.table).
		Where("user_id = ?", userId).
		WhereOptional(isOpenResult == 1, "(result = 0 OR result IS NULL)").
		WhereOptional(isOpenResult == 2, "result IS NOT NULL AND result != 0")

	if startTime != nil {
		builder.Where("create_time >= ?", time.Unix(*startTime, 0).Format("2006-01-02 15:04:05"))
	}

	if endTime != nil {
		builder.Where("create_time <= ?", time.Unix(*endTime, 0).Format("2006-01-02 15:04:05"))
	}

	builder.OrderBy("create_time", "DESC")

	var records []*UserNabGuessRecord
	err := builder.QueryRows(ctx, m.conn, &records)
	return records, err
}

// GetUserTotalRewardAmount 获取用户的NBA总奖励金额
func (m *defaultUserNabGuessRecordModel) GetUserTotalRewardAmount(ctx context.Context, userId int64) (float64, error) {
	builder := utils.NewSQLBuilder().
		Select("COALESCE(SUM(reward_amount), 0) as total_reward").
		From(m.table).
		Where("user_id = ?", userId)

	query, args := builder.Build()

	var totalReward float64
	err := m.conn.QueryRowCtx(ctx, &totalReward, query, args...)
	return totalReward, err
}
