package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type UserGuessRecord struct {
	Id            int64     `db:"id"`
	UserId        int64     `db:"user_id"`
	MatchId       int       `db:"match_id"`
	Type          int       `db:"type"`
	RewardAmount  float64   `db:"reward_amount"`
	IsHeavyHammer int       `db:"is_heavy_hammer"`
	WhoWin        int       `db:"who_win"`
	Result        int       `db:"result"`
	RoundNum      int       `db:"round_num"`
	CompShortZh   string    `db:"comp_short_zh"`
	CreateTime    time.Time `db:"create_time"`
	UpdateTime    time.Time `db:"update_time"`
}

type UserGuessRecordModel interface {
	Insert(ctx context.Context, data *UserGuessRecord) (sql.Result, error)
	BatchInsert(ctx context.Context, data []*UserGuessRecord) error
	FindByUserAndMatch(ctx context.Context, userId int64, matchIds []int) ([]*UserGuessRecord, error)
	CountUserGuessInRound(ctx context.Context, userId int64, compShortZh string, roundNum int) (int64, error)
	CheckExistingRecords(ctx context.Context, userId int64, records []*UserGuessRecord) (map[string]bool, error)
	FindMyGuessList(ctx context.Context, userId int64, compShortZh string, roundNum int, isOpenResult int) ([]*UserGuessRecord, error)
	FindOne(ctx context.Context, id int64) (*UserGuessRecord, error)
	Update(ctx context.Context, data *UserGuessRecord) error
	CheckHeavyHammerExists(ctx context.Context, userId int64, compShortZh string, roundNum int) (bool, error)
	SelectCheckHeavyHammers(ctx context.Context, userId int64, compShortZh []string, roundNum []int) ([]*HeavyHammerCheck, error)
	GetProfitRanking(ctx context.Context, startTime, endTime string) ([]*ProfitRankingData, error)
	GetHeavyHammerRanking(ctx context.Context, startTime, endTime string) ([]*HeavyHammerRankingData, error)
	GetUserTotalRewardAmount(ctx context.Context, userId int64) (float64, error)
}

// 盈利排行榜数据结构
type ProfitRankingData struct {
	UserId      int64   `db:"user_id"`
	WinCount    int     `db:"win_count"`
	LossCount   int     `db:"loss_count"`
	TotalReward float64 `db:"total_reward"`
}

// 重锤命中率排行榜数据结构
type HeavyHammerRankingData struct {
	UserId     int64 `db:"user_id"`
	HitCount   int   `db:"hit_count"`
	TotalCount int   `db:"total_count"`
}

type defaultUserGuessRecordModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserGuessRecordModel(conn sqlx.SqlConn) UserGuessRecordModel {
	return &defaultUserGuessRecordModel{
		conn:  conn,
		table: "`db_user_guess_record`",
	}
}

func (m *defaultUserGuessRecordModel) Insert(ctx context.Context, data *UserGuessRecord) (sql.Result, error) {
	query := `insert into ` + m.table + ` (user_id, match_id, type, reward_amount, is_heavy_hammer, who_win) values (?, ?, ?, ?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.UserId, data.MatchId, data.Type, data.RewardAmount, data.IsHeavyHammer, data.WhoWin)
}

func (m *defaultUserGuessRecordModel) BatchInsert(ctx context.Context, data []*UserGuessRecord) error {
	if len(data) == 0 {
		return nil
	}

	query := `insert into ` + m.table + ` (user_id, match_id, type, reward_amount, is_heavy_hammer, who_win, round_num, comp_short_zh) values `
	values := make([]interface{}, 0, len(data)*8)

	for i, record := range data {
		if i > 0 {
			query += ", "
		}
		query += "(?, ?, ?, ?, ?, ?, ?, ?)"

		values = append(values, record.UserId, record.MatchId, record.Type, record.RewardAmount, record.IsHeavyHammer, record.WhoWin, record.RoundNum, record.CompShortZh)
	}

	_, err := m.conn.ExecCtx(ctx, query, values...)
	return err
}

func (m *defaultUserGuessRecordModel) FindByUserAndMatch(ctx context.Context, userId int64, matchIds []int) ([]*UserGuessRecord, error) {
	if len(matchIds) == 0 {
		return []*UserGuessRecord{}, nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	// 使用SQLBuilder构建查询
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "round_num", "comp_short_zh", "create_time", "update_time").
		From(m.table).
		Where("user_id = ?", userId).
		WhereIn("match_id", values).
		Limit(60)

	var records []*UserGuessRecord
	err := builder.QueryRows(ctx, m.conn, &records)
	switch err {
	case nil:
		return records, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserGuessRecordModel) CountUserGuessInRound(ctx context.Context, userId int64, compShortZh string, roundNum int) (int64, error) {
	// 使用优化后的SQL构建器，支持JOIN查询
	builder := utils.NewSQLBuilder().
		Select("COUNT(*)").
		From(m.table+" ugr").
		Join("db_matches_schedule ms", "ugr.match_id = ms.match_id").
		Join("db_football_match fm", "ugr.match_id = fm.id").
		Where("ugr.user_id = ?", userId).
		Where("ms.comp_short_zh = ?", compShortZh).
		Where("fm.round_num = ?", roundNum)

	query, args := builder.Build()

	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, args...)
	return count, err
}

// CheckExistingRecords 检查哪些记录已经存在
func (m *defaultUserGuessRecordModel) CheckExistingRecords(ctx context.Context, userId int64, records []*UserGuessRecord) (map[string]bool, error) {
	if len(records) == 0 {
		return make(map[string]bool), nil
	}

	// 构建查询条件
	conditions := make([]string, 0, len(records))
	args := make([]interface{}, 0, len(records)*3+1)
	args = append(args, userId)

	for _, record := range records {
		conditions = append(conditions, "(match_id = ? and type = ?)")
		args = append(args, record.MatchId, record.Type)
	}

	query := `select match_id, type from ` + m.table + ` where user_id = ? and (` + strings.Join(conditions, " or ") + `)`

	type ExistingRecord struct {
		MatchId int `db:"match_id"`
		Type    int `db:"type"`
	}

	var existingRecords []*ExistingRecord
	err := m.conn.QueryRowsCtx(ctx, &existingRecords, query, args...)
	if err != nil {
		return nil, err
	}

	// 构建存在记录的map
	existingMap := make(map[string]bool)
	for _, existing := range existingRecords {
		key := fmt.Sprintf("%d_%d", existing.MatchId, existing.Type)
		existingMap[key] = true
	}

	return existingMap, nil
}

// FindMyGuessList 查询我的竞猜列表
func (m *defaultUserGuessRecordModel) FindMyGuessList(ctx context.Context, userId int64, compShortZh string, roundNum int, isOpenResult int) ([]*UserGuessRecord, error) {
	// 使用优化后的SQL构建器
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "round_num", "comp_short_zh", "create_time", "update_time").
		From(m.table).
		Where("user_id = ?", userId).
		WhereEq("comp_short_zh", compShortZh).
		WhereEq("round_num", roundNum).
		WhereOptional(isOpenResult == 1, "(result = 0 OR result IS NULL)").
		WhereOptional(isOpenResult == 2, "result IS NOT NULL AND result != 0").
		OrderBy("create_time", "DESC")

	var records []*UserGuessRecord
	err := builder.QueryRows(ctx, m.conn, &records)
	return records, err
}

// FindOne 根据ID查询单条记录
func (m *defaultUserGuessRecordModel) FindOne(ctx context.Context, id int64) (*UserGuessRecord, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "match_id", "type", "reward_amount", "is_heavy_hammer", "who_win", "result", "round_num", "comp_short_zh", "create_time", "update_time").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp UserGuessRecord
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// Update 更新记录
func (m *defaultUserGuessRecordModel) Update(ctx context.Context, data *UserGuessRecord) error {
	query := `update ` + m.table + ` set user_id = ?, match_id = ?, type = ?, reward_amount = ?, is_heavy_hammer = ?, who_win = ?, result = ?, round_num = ?, comp_short_zh = ?, update_time = ? where id = ?`
	_, err := m.conn.ExecCtx(ctx, query, data.UserId, data.MatchId, data.Type, data.RewardAmount, data.IsHeavyHammer, data.WhoWin, data.Result, data.RoundNum, data.CompShortZh, data.UpdateTime, data.Id)
	return err
}

// CheckHeavyHammerExists 检查用户在指定联赛和轮次是否已有重锤单
func (m *defaultUserGuessRecordModel) CheckHeavyHammerExists(ctx context.Context, userId int64, compShortZh string, roundNum int) (bool, error) {
	query := `select count(*) from ` + m.table + ` where user_id = ? and comp_short_zh = ? and round_num = ? and is_heavy_hammer = 1`

	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId, compShortZh, roundNum)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

type HeavyHammerCheck struct {
	Id          int64  `db:"id"`
	RoundNum    int    `db:"round_num"`
	CompShortZh string `db:"comp_short_zh"`
}

func (m *defaultUserGuessRecordModel) SelectCheckHeavyHammers(ctx context.Context, userId int64, compShortZh []string, roundNum []int) ([]*HeavyHammerCheck, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "round_num", "comp_short_zh").
		From(m.table).
		Where("user_id = ?", userId).
		Where("is_heavy_hammer = 1").
		WhereIn("comp_short_zh", utils.SliceToInterface(compShortZh)).
		WhereIn("round_num", utils.SliceToInterface(roundNum))

	var records []*HeavyHammerCheck
	err := builder.QueryRows(ctx, m.conn, &records)
	return records, err
}

func (m *defaultUserGuessRecordModel) GetProfitRanking(ctx context.Context, startTime, endTime string) ([]*ProfitRankingData, error) {

	builder := utils.NewSQLBuilder().
		Select(
			"user_id",
			"SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) as win_count",
			"SUM(CASE WHEN result = 2 THEN 1 ELSE 0 END) as loss_count",
			"SUM(reward_amount) as total_reward",
		).
		From(m.table).
		WhereOptional(startTime != "", "create_time >= ?", startTime).
		WhereOptional(endTime != "", "create_time <= ?", endTime).
		Where("result > 0").
		GroupBy("user_id").
		OrderBy("total_reward", "DESC").
		Limit(20)

	query, args := builder.Build()

	var rankings []*ProfitRankingData
	err := m.conn.QueryRowsCtx(ctx, &rankings, query, args...)
	return rankings, err
}

func (m *defaultUserGuessRecordModel) GetHeavyHammerRanking(ctx context.Context, startTime, endTime string) ([]*HeavyHammerRankingData, error) {

	builder := utils.NewSQLBuilder().
		Select(
			"user_id",
			"SUM(CASE WHEN is_heavy_hammer = 1 AND result = 1 THEN 1 ELSE 0 END) as hit_count",
			"SUM(CASE WHEN is_heavy_hammer = 1 THEN 1 ELSE 0 END) as total_count",
		).
		From(m.table).
		WhereOptional(startTime != "", "create_time >= ?", startTime).
		WhereOptional(endTime != "", "create_time <= ?", endTime).
		Where("result > 0").
		GroupBy("user_id").
		Having("total_count > 0").
		OrderBy("(hit_count / total_count)", "DESC").
		OrderBy("hit_count", "DESC").
		OrderBy("user_id", "ASC").
		Limit(20)

	query, args := builder.Build()

	var rankings []*HeavyHammerRankingData
	err := m.conn.QueryRowsCtx(ctx, &rankings, query, args...)
	return rankings, err
}

// GetUserTotalRewardAmount 获取用户的总奖励金额
func (m *defaultUserGuessRecordModel) GetUserTotalRewardAmount(ctx context.Context, userId int64) (float64, error) {
	builder := utils.NewSQLBuilder().
		Select("COALESCE(SUM(reward_amount), 0) as total_reward").
		From(m.table).
		Where("user_id = ?", userId)

	query, args := builder.Build()

	var totalReward float64
	err := m.conn.QueryRowCtx(ctx, &totalReward, query, args...)
	return totalReward, err
}
