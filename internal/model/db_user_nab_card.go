package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type UserNabCard struct {
	Id         int       `db:"id"`
	UserId     int64     `db:"user_id"`
	NbaCardId  int       `db:"nba_card_id"`
	Num        int       `db:"num"`
	CreateTime time.Time `db:"create_time"`
}

type UserNabCardModel interface {
	Insert(ctx context.Context, data *UserNabCard) (sql.Result, error)
	FindOne(ctx context.Context, id int64) (*UserNabCard, error)
	FindByUserAndCard(ctx context.Context, userId int64, nbaCardId int) (*UserNabCard, error)
	Update(ctx context.Context, data *UserNabCard) error
	IncrementCardNum(ctx context.Context, userId int64, nbaCardId int) error
	DecrementCardNum(ctx context.Context, userId int64, nbaCardId int) error
	FindByUserId(ctx context.Context, userId int64) ([]*UserNabCard, error)
}

type defaultUserNabCardModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserNabCardModel(conn sqlx.SqlConn) UserNabCardModel {
	return &defaultUserNabCardModel{
		conn:  conn,
		table: "`db_user_nab_card`",
	}
}

func (m *defaultUserNabCardModel) Insert(ctx context.Context, data *UserNabCard) (sql.Result, error) {
	query := `insert into ` + m.table + ` (user_id, nba_card_id, num) values (?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.UserId, data.NbaCardId, data.Num)
}

func (m *defaultUserNabCardModel) FindOne(ctx context.Context, id int64) (*UserNabCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "nba_card_id", "num", "create_time").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp UserNabCard
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserNabCardModel) FindByUserAndCard(ctx context.Context, userId int64, nbaCardId int) (*UserNabCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "nba_card_id", "num", "create_time").
		From(m.table).
		Where("user_id = ?", userId).
		Where("nba_card_id = ?", nbaCardId).
		Limit(1)

	var resp UserNabCard
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserNabCardModel) Update(ctx context.Context, data *UserNabCard) error {
	query := `update ` + m.table + ` set user_id = ?, nba_card_id = ?, num = ? where id = ?`
	_, err := m.conn.ExecCtx(ctx, query, data.UserId, data.NbaCardId, data.Num, data.Id)
	return err
}

func (m *defaultUserNabCardModel) IncrementCardNum(ctx context.Context, userId int64, nbaCardId int) error {
	query := `insert into ` + m.table + ` (user_id, nba_card_id, num) values (?, ?, 1) 
			  on duplicate key update num = num + 1`
	_, err := m.conn.ExecCtx(ctx, query, userId, nbaCardId)
	return err
}

func (m *defaultUserNabCardModel) DecrementCardNum(ctx context.Context, userId int64, nbaCardId int) error {
	query := `update ` + m.table + ` set num = num - 1 where user_id = ? and nba_card_id = ? and num > 0`
	_, err := m.conn.ExecCtx(ctx, query, userId, nbaCardId)
	return err
}

func (m *defaultUserNabCardModel) FindByUserId(ctx context.Context, userId int64) ([]*UserNabCard, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "user_id", "nba_card_id", "num", "create_time").
		From(m.table).
		Where("user_id = ?", userId)

	var cards []*UserNabCard
	err := builder.QueryRows(ctx, m.conn, &cards)
	return cards, err
}