package model

import (
	"context"
	"database/sql"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type User struct {
	Id                     int64      `db:"id"`
	ThirdUserId            string     `db:"third_user_id"`
	Username               string     `db:"username"`
	PlatformId             string     `db:"platform_id"`
	NbaLotteryRemainCount  int        `db:"nba_lottery_remain_count"`
	CreateTime             *time.Time `db:"create_time"`
	UpdateTime             *time.Time `db:"update_time"`
}

type UserModel interface {
	Insert(ctx context.Context, data *User) (sql.Result, error)
	FindOne(ctx context.Context, id int64) (*User, error)
	FindByThirdUserId(ctx context.Context, thirdUserId string) (*User, error)
	FindByIds(ctx context.Context, ids []int64) ([]*User, error)
	FindByConditions(ctx context.Context, conditions map[string]interface{}) (*User, error)
	ExistsByThirdUserAndPlatform(ctx context.Context, thirdUserId, name string, platformId string) (*User, error)
	DecrementNbaLotteryCount(ctx context.Context, userId int64) error
}

type defaultUserModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserModel(conn sqlx.SqlConn) UserModel {
	return &defaultUserModel{
		conn:  conn,
		table: "`db_user`",
	}
}

func (m *defaultUserModel) Insert(ctx context.Context, data *User) (sql.Result, error) {
	query := `INSERT INTO ` + m.table + ` (third_user_id, username, platform_id, nba_lottery_remain_count) VALUES (?, ?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.ThirdUserId, data.Username, data.PlatformId, data.NbaLotteryRemainCount)
}

func (m *defaultUserModel) FindOne(ctx context.Context, id int64) (*User, error) {
	query := `select id, third_user_id, username, platform_id, nba_lottery_remain_count, create_time, update_time from ` + m.table + ` where id = ? limit 1`
	var resp User
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindByThirdUserId(ctx context.Context, thirdUserId string) (*User, error) {
	query := `select id, third_user_id, username, platform_id, nba_lottery_remain_count, create_time, update_time from ` + m.table + ` where third_user_id = ? limit 1`
	var resp User
	err := m.conn.QueryRowCtx(ctx, &resp, query, thirdUserId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindByIds(ctx context.Context, ids []int64) ([]*User, error) {
	if len(ids) == 0 {
		return []*User{}, nil
	}

	// 构建 IN 查询的占位符
	placeholders := make([]string, len(ids))
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = "?"
		args[i] = id
	}

	query := `select id, third_user_id, username, platform_id, nba_lottery_remain_count, create_time, update_time from ` + m.table + ` where id in (` +
		strings.Join(placeholders, ",") + `)`

	var users []*User
	err := m.conn.QueryRowsCtx(ctx, &users, query, args...)
	if err != nil {
		return nil, err
	}

	return users, nil
}

// FindByConditions 根据条件查找用户
func (m *defaultUserModel) FindByConditions(ctx context.Context, conditions map[string]interface{}) (*User, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "third_user_id", "username", "platform_id", "nba_lottery_remain_count", "create_time", "update_time").
		From(m.table).
		Limit(1)

	for field, value := range conditions {
		builder.WhereEq(field, value)
	}

	var resp User
	err := builder.QueryRowReturnNotFound(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// ExistsByThirdUserAndPlatform 检查用户是否存在（三个字段组合）
func (m *defaultUserModel) ExistsByThirdUserAndPlatform(ctx context.Context, thirdUserId, name string, platformId string) (*User, error) {
	conditions := map[string]interface{}{
		"third_user_id": thirdUserId,
		"platform_id":   platformId,
		"username":      name,
	}
	return m.FindByConditions(ctx, conditions)
}

// DecrementNbaLotteryCount 减少用户NBA抽奖次数
func (m *defaultUserModel) DecrementNbaLotteryCount(ctx context.Context, userId int64) error {
	query := `update ` + m.table + ` set nba_lottery_remain_count = nba_lottery_remain_count - 1, update_time = NOW() where id = ? and nba_lottery_remain_count > 0`
	result, err := m.conn.ExecCtx(ctx, query, userId)
	if err != nil {
		return err
	}
	
	// 检查是否有行被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	
	if rowsAffected == 0 {
		return ErrNotFound // 表示没有可用的抽奖次数
	}
	
	return nil
}
