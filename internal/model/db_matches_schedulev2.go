package model

import (
	"context"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type MatchScheduleV2 struct {
	Id               int64   `db:"id"`
	MatchTime        int64   `db:"match_time"`
	SportId          int     `db:"sport_id"`
	Comp             string  `db:"comp"`
	CompLogo         string  `db:"comp_logo"`
	Home             string  `db:"home"`
	HomeLogo         string  `db:"home_logo"`
	Away             string  `db:"away"`
	AwayLogo         string  `db:"away_logo"`
	Flag             int     `db:"flag"`
	CompShortZh      string  `db:"comp_short_zh"`
	CompShortEn      string  `db:"comp_short_en"`
	HomeEn           string  `db:"home_en"`
	AwayEn           string  `db:"away_en"`
	CloseTimeSeconds int     `db:"close_time_seconds"`
	ConcedeNum       float64 `db:"concede_num"`
	OverUnderNum     float64 `db:"over_under_num"`
	HandicapStatus   int     `db:"handicap_status"`
	HomeGoals        int     `db:"home_goals"`
	AwayGoals        int     `db:"away_goals"`
}

type MatchScheduleV2Model interface {
	FindOne(ctx context.Context, id int64) (*MatchScheduleV2, error)
	FindByDateRange(ctx context.Context, startTime, endTime int64) ([]*MatchScheduleV2, error)
	FindByMatchIds(ctx context.Context, matchIds []int64) ([]*MatchScheduleV2, error)
}

type defaultMatchScheduleV2Model struct {
	conn  sqlx.SqlConn
	table string
}

func NewMatchScheduleV2Model(conn sqlx.SqlConn) MatchScheduleV2Model {
	return &defaultMatchScheduleV2Model{
		conn:  conn,
		table: "`db_matches_schedulev2`",
	}
}

func (m *defaultMatchScheduleV2Model) FindOne(ctx context.Context, id int64) (*MatchScheduleV2, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "match_time", "sport_id", "comp", "comp_logo", "home", "home_logo", "away", "away_logo", "flag", "comp_short_zh", "comp_short_en", "home_en", "away_en", "close_time_seconds", "concede_num", "over_under_num", "handicap_status", "home_goals", "away_goals").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp MatchScheduleV2
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultMatchScheduleV2Model) FindByDateRange(ctx context.Context, startTime, endTime int64) ([]*MatchScheduleV2, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "match_time", "sport_id", "comp", "comp_logo", "home", "home_logo", "away", "away_logo", "flag", "comp_short_zh", "comp_short_en", "home_en", "away_en", "close_time_seconds", "concede_num", "over_under_num", "handicap_status", "home_goals", "away_goals").
		From(m.table).
		Where("match_time >= ?", startTime).
		Where("match_time <= ?", endTime).
		OrderBy("match_time", "ASC").
		OrderBy("id", "ASC")

	var schedules []*MatchScheduleV2
	err := builder.QueryRows(ctx, m.conn, &schedules)
	return schedules, err
}

func (m *defaultMatchScheduleV2Model) FindByMatchIds(ctx context.Context, matchIds []int64) ([]*MatchScheduleV2, error) {
	if len(matchIds) == 0 {
		return []*MatchScheduleV2{}, nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	builder := utils.NewSQLBuilder().
		Select("id", "match_time", "sport_id", "comp", "comp_logo", "home", "home_logo", "away", "away_logo", "flag", "comp_short_zh", "comp_short_en", "home_en", "away_en", "close_time_seconds", "concede_num", "over_under_num", "handicap_status", "home_goals", "away_goals").
		From(m.table).
		WhereIn("id", values)

	var schedules []*MatchScheduleV2
	err := builder.QueryRows(ctx, m.conn, &schedules)
	return schedules, err
}