package model

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type SystemConfig struct {
	Id          int        `db:"id"`
	Config<PERSON>ey   string     `db:"config_key"`
	ConfigValue string     `db:"config_value"`
	Notice      string     `db:"notice"`
	CreateTime  *time.Time `db:"create_time"`
	UpdateTime  *time.Time `db:"update_time"`
}

type SystemConfigModel interface {
	FindByConfigKey(ctx context.Context, configKey string) (*SystemConfig, error)
}

type defaultSystemConfigModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewSystemConfigModel(conn sqlx.SqlConn) SystemConfigModel {
	return &defaultSystemConfigModel{
		conn:  conn,
		table: "`db_system_config`",
	}
}

func (m *defaultSystemConfigModel) FindByConfigKey(ctx context.Context, configKey string) (*SystemConfig, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "config_key", "config_value", "notice", "create_time", "update_time").
		From(m.table).
		WhereEq("config_key", configKey).
		Limit(1)

	var resp SystemConfig
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}