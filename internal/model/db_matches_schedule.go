package model

import (
	"context"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type MatchSchedule struct {
	Id                int64  `db:"id"`
	MatchId           int    `db:"match_id"`
	MatchTime         int64  `db:"match_time"`
	SportId           int    `db:"sport_id"`
	Comp              string `db:"comp"`
	LeagueId6t        int    `db:"league_id_6t"`
	CompLogo          string `db:"comp_logo"`
	Home              string `db:"home"`
	HomeLogo          string `db:"home_logo"`
	Away              string `db:"away"`
	AwayLogo          string `db:"away_logo"`
	HomeScore         int    `db:"home_score"`
	AwayScore         int    `db:"away_score"`
	TimePlayed        int    `db:"time_played"`
	MatchStatus       int    `db:"match_status"`
	MatchDetailStatus string `db:"match_detail_status"`
	Flag              int    `db:"flag"`
	RawMatchStatus    int    `db:"raw_match_status"`
	CompShortZh       string `db:"comp_short_zh"`
	Focus             int    `db:"focus"`
	CompShortEn       string `db:"comp_short_en"`
	HomeEn            string `db:"home_en"`
	AwayEn            string `db:"away_en"`
}

type MatchScheduleModel interface {
	FindOne(ctx context.Context, id int64) (*MatchSchedule, error)
	FindByCompAndRound(ctx context.Context, compShortZh string, roundNum int) ([]*MatchSchedule, error)
	CheckMatchExists(ctx context.Context, matchIds []int) (map[int]bool, error)
	FindByMatchId(ctx context.Context, matchId int) (*MatchSchedule, error)
	FindByMatchIds(ctx context.Context, matchIds []int) ([]*MatchSchedule, error)
}

type defaultMatchScheduleModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewMatchScheduleModel(conn sqlx.SqlConn) MatchScheduleModel {
	return &defaultMatchScheduleModel{
		conn:  conn,
		table: "`db_matches_schedule`",
	}
}

func (m *defaultMatchScheduleModel) FindOne(ctx context.Context, id int64) (*MatchSchedule, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "match_id", "match_time", "sport_id", "comp", "league_id_6t", "comp_logo", "home", "home_logo", "away", "away_logo", "home_score", "away_score", "time_played", "match_status", "match_detail_status", "flag", "raw_match_status", "comp_short_zh", "focus", "comp_short_en", "home_en", "away_en").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp MatchSchedule
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultMatchScheduleModel) FindByCompAndRound(ctx context.Context, compShortZh string, roundNum int) ([]*MatchSchedule, error) {
	// 使用SQLBuilder构建复杂JOIN查询，自动处理NULL值
	builder := utils.NewSQLBuilder().
		Select(
			"ms.id",
			"ms.match_id",
			"ms.match_time",
			"ms.sport_id",
			"ms.comp",
			"ms.league_id_6t",
			"ms.comp_logo",
			"ms.home",
			"ms.home_logo",
			"ms.away",
			"ms.away_logo",
			"ms.home_score",
			"ms.away_score",
			"ms.time_played",
			"ms.match_status",
			"ms.match_detail_status",
			"ms.flag",
			"ms.raw_match_status",
			"ms.comp_short_zh",
			"ms.focus",
			"ms.comp_short_en",
			"ms.home_en",
			"ms.away_en",
		).
		From(m.table+" ms").
		Join("db_football_match fm", "ms.match_id = fm.id").
		Where("ms.comp_short_zh = ?", compShortZh).
		Where("fm.round_num = ?", roundNum).
		OrderBy("ms.match_time", "ASC").OrderBy("ms.id", "ASC")

	var schedules []*MatchSchedule
	err := builder.QueryRows(ctx, m.conn, &schedules)
	return schedules, err
}

// CheckMatchExists 检查match_id是否在db_matches_schedule表中存在
func (m *defaultMatchScheduleModel) CheckMatchExists(ctx context.Context, matchIds []int) (map[int]bool, error) {
	if len(matchIds) == 0 {
		return make(map[int]bool), nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	// 使用SQLBuilder构建IN查询
	builder := utils.NewSQLBuilder().
		Select("match_id").
		From(m.table).
		WhereIn("match_id", values)

	type MatchIdResult struct {
		MatchId int `db:"match_id"`
	}

	var results []*MatchIdResult
	err := builder.QueryRows(ctx, m.conn, &results)
	if err != nil {
		return nil, err
	}

	// 构建存在的match_id map
	existsMap := make(map[int]bool)
	for _, result := range results {
		existsMap[result.MatchId] = true
	}

	return existsMap, nil
}

// FindByMatchId 根据match_id查询赛程信息
func (m *defaultMatchScheduleModel) FindByMatchId(ctx context.Context, matchId int) (*MatchSchedule, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "match_id", "match_time", "sport_id", "comp", "league_id_6t", "comp_logo", "home", "home_logo", "away", "away_logo", "home_score", "away_score", "time_played", "match_status", "match_detail_status", "flag", "raw_match_status", "comp_short_zh", "focus", "comp_short_en", "home_en", "away_en").
		From(m.table).
		Where("match_id = ?", matchId).
		Limit(1)

	var resp MatchSchedule
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// FindByMatchIds 根据match_id列表批量查询赛程信息
func (m *defaultMatchScheduleModel) FindByMatchIds(ctx context.Context, matchIds []int) ([]*MatchSchedule, error) {
	if len(matchIds) == 0 {
		return []*MatchSchedule{}, nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(matchIds))
	for i, matchId := range matchIds {
		values[i] = matchId
	}

	// 使用SQLBuilder构建批量查询
	builder := utils.NewSQLBuilder().
		Select("id", "match_id", "match_time", "sport_id", "comp", "league_id_6t", "comp_logo", "home", "home_logo", "away", "away_logo", "home_score", "away_score", "time_played", "match_status", "match_detail_status", "flag", "raw_match_status", "comp_short_zh", "focus", "comp_short_en", "home_en", "away_en").
		From(m.table).
		WhereIn("match_id", values)

	var schedules []*MatchSchedule
	err := builder.QueryRows(ctx, m.conn, &schedules)
	return schedules, err
}
