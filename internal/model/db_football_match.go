package model

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/constants"
	"sport-server/internal/utils"
)

type FootballMatch struct {
	Id               int64   `db:"id"`
	SeasonId         int     `db:"season_id"`
	CompetitionId    int     `db:"competition_id"`
	HomeTeamId       int     `db:"home_team_id"`
	AwayTeamId       int     `db:"away_team_id"`
	StatusId         int     `db:"status_id"`
	TimePlayed       int     `db:"time_played"`
	MatchTime        int64   `db:"match_time"`
	Neutral          int     `db:"neutral"`
	Note             string  `db:"note"`
	HomeScores       string  `db:"home_scores"`
	AwayScores       string  `db:"away_scores"`
	HomePosition     string  `db:"home_position"`
	AwayPosition     string  `db:"away_position"`
	Mlive            int     `db:"mlive"`
	Intelligence     int     `db:"intelligence"`
	Lineup           int     `db:"lineup"`
	VenueId          int     `db:"venue_id"`
	RefereeId        int     `db:"referee_id"`
	RelatedId        int     `db:"related_id"`
	AggScore         string  `db:"agg_score"`
	UpdatedAt        int     `db:"updated_at"`
	RoundNum         int     `db:"round_num"`
	CloseTimeSeconds int     `db:"close_time_seconds"`
	ConcedeNum       float64 `db:"concede_num"`
	OverUnderNum     float64 `db:"over_under_num"`
	HandicapStatus   int     `db:"handicap_status"`
	HomeGoals        int     `db:"home_goals"`
	AwayGoals        int     `db:"away_goals"`
}

type DefaultRoundItem struct {
	CompShortZh string `json:"comp_short_zh"`
	RoundNum    int    `json:"round_num"`
}

type FootballMatchModel interface {
	FindOne(ctx context.Context, id int64) (*FootballMatch, error)
	FindByCompAndRound(ctx context.Context, compShortZh string, roundNum int) ([]*FootballMatch, error)
	GetCloseTime(ctx context.Context, matchId int) (time.Time, error)
	FindByIds(ctx context.Context, ids []int64) ([]*FootballMatch, error)
	FindDefaultRound(ctx context.Context) ([]*DefaultRoundItem, error)
}

type defaultFootballMatchModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewFootballMatchModel(conn sqlx.SqlConn) FootballMatchModel {
	return &defaultFootballMatchModel{
		conn:  conn,
		table: "`db_football_match`",
	}
}

func (m *defaultFootballMatchModel) FindOne(ctx context.Context, id int64) (*FootballMatch, error) {
	// 使用SQLBuilder和自动NULL转换
	builder := utils.NewSQLBuilder().
		Select("id", "season_id", "competition_id", "home_team_id", "away_team_id", "status_id", "time_played", "match_time", "neutral", "note", "home_scores", "away_scores", "home_position", "away_position", "mlive", "intelligence", "lineup", "venue_id", "referee_id", "related_id", "agg_score", "updated_at", "round_num", "close_time_seconds", "concede_num", "over_under_num", "handicap_status", "home_goals", "away_goals").
		From(m.table).
		Where("id = ?", id).
		Limit(1)

	var resp FootballMatch
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFootballMatchModel) FindByCompAndRound(ctx context.Context, compShortZh string, roundNum int) ([]*FootballMatch, error) {
	// 使用SQLBuilder构建复杂JOIN查询，自动处理NULL值
	builder := utils.NewSQLBuilder().
		Select(
			"fm.id",
			"fm.season_id",
			"fm.competition_id",
			"fm.home_team_id",
			"fm.away_team_id",
			"fm.status_id",
			"fm.time_played",
			"fm.match_time",
			"fm.neutral",
			"fm.note",
			"fm.home_scores",
			"fm.away_scores",
			"fm.home_position",
			"fm.away_position",
			"fm.mlive",
			"fm.intelligence",
			"fm.lineup",
			"fm.venue_id",
			"fm.referee_id",
			"fm.related_id",
			"fm.agg_score",
			"fm.updated_at",
			"fm.round_num",
			"fm.close_time_seconds",
			"fm.concede_num",
			"fm.over_under_num",
			"fm.handicap_status",
			"fm.home_goals",
			"fm.away_goals",
		).
		From(m.table+" fm").
		Join("db_matches_schedule ms", "fm.id = ms.match_id").
		Where("ms.comp_short_zh = ?", compShortZh).
		Where("fm.round_num = ?", roundNum).
		OrderBy("ms.match_time", "ASC").OrderBy("ms.id", "ASC")

	var matches []*FootballMatch
	err := builder.QueryRows(ctx, m.conn, &matches)
	return matches, err
}

func (m *defaultFootballMatchModel) GetCloseTime(ctx context.Context, matchId int) (time.Time, error) {
	// 使用SQLBuilder构建JOIN查询
	builder := utils.NewSQLBuilder().
		Select("ms.match_time + fm.close_time_seconds as close_time").
		From("db_matches_schedule ms").
		Join(m.table+" fm", "ms.match_id = fm.id").
		Where("ms.match_id = ?", matchId)

	type CloseTimeResult struct {
		CloseTime int64 `db:"close_time"`
	}

	var result CloseTimeResult
	err := builder.QueryRow(ctx, m.conn, &result)
	if err != nil {
		return time.Time{}, err
	}

	return time.Unix(result.CloseTime, 0), nil
}

// FindByIds 根据ID列表批量查询比赛信息
func (m *defaultFootballMatchModel) FindByIds(ctx context.Context, ids []int64) ([]*FootballMatch, error) {
	if len(ids) == 0 {
		return []*FootballMatch{}, nil
	}

	// 转换为interface{}切片
	values := make([]interface{}, len(ids))
	for i, id := range ids {
		values[i] = id
	}

	// 使用SQLBuilder构建批量查询
	builder := utils.NewSQLBuilder().
		Select("id", "season_id", "competition_id", "home_team_id", "away_team_id", "status_id", "time_played", "match_time", "neutral", "note", "home_scores", "away_scores", "home_position", "away_position", "mlive", "intelligence", "lineup", "venue_id", "referee_id", "related_id", "agg_score", "updated_at", "round_num", "close_time_seconds", "concede_num", "over_under_num", "handicap_status", "home_goals", "away_goals").
		From(m.table).
		WhereIn("id", values)

	var matches []*FootballMatch
	err := builder.QueryRows(ctx, m.conn, &matches)
	return matches, err
}

// FindDefaultRound 查询默认轮次信息
func (m *defaultFootballMatchModel) FindDefaultRound(ctx context.Context) ([]*DefaultRoundItem, error) {
	// 使用SQLBuilder构建复杂查询，查询最小轮次
	builder := utils.NewSQLBuilder().
		Select(
			"a.comp_short_zh",
			"MIN(b.round_num) as round_num",
		).
		From("db_matches_schedule a").
		LeftJoin("db_football_match b", "a.match_id = b.id").
		WhereIn("b.handicap_status", []interface{}{constants.HandicapStatusNotOpen, constants.HandicapStatusOpen, constants.HandicapStatusClosed}).
		WhereIn("a.comp_short_zh", utils.SliceToInterface(constants.CompShortZhMaps)).
		GroupBy("a.comp_short_zh")

	var results []*DefaultRoundItem
	err := builder.QueryRows(ctx, m.conn, &results)
	return results, err
}
