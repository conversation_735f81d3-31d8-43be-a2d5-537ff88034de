package model

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"sport-server/internal/utils"
)

type NabCardGroups struct {
	Id          int        `db:"id"`
	Title       string     `db:"title"`
	NbaCardIds  string     `db:"nba_card_ids"`
	RewardType  int        `db:"reward_type"`
	RewardValue string     `db:"reward_value"`
	CreateTime  time.Time  `db:"create_time"`
	UpdateTime  time.Time  `db:"update_time"`
	DeletedAt   *time.Time `db:"deleted_at"`
}

type NabCardGroupsModel interface {
	FindOne(ctx context.Context, id int64) (*NabCardGroups, error)
	FindAll(ctx context.Context) ([]*NabCardGroups, error)
}

type defaultNabCardGroupsModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewNabCardGroupsModel(conn sqlx.SqlConn) NabCardGroupsModel {
	return &defaultNabCardGroupsModel{
		conn:  conn,
		table: "`db_nab_card_groups`",
	}
}

func (m *defaultNabCardGroupsModel) FindOne(ctx context.Context, id int64) (*NabCardGroups, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "title", "nba_card_ids", "reward_type", "reward_value", "create_time", "update_time", "deleted_at").
		From(m.table).
		Where("id = ?", id).
		Where("deleted_at IS NULL").
		Limit(1)

	var resp NabCardGroups
	err := builder.QueryRow(ctx, m.conn, &resp)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNabCardGroupsModel) FindAll(ctx context.Context) ([]*NabCardGroups, error) {
	builder := utils.NewSQLBuilder().
		Select("id", "title", "nba_card_ids", "reward_type", "reward_value", "create_time", "update_time", "deleted_at").
		From(m.table).
		Where("deleted_at IS NULL")

	var groups []*NabCardGroups
	err := builder.QueryRows(ctx, m.conn, &groups)
	return groups, err
}
