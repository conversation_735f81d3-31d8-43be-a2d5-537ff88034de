package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type UserNabexchangeRecord struct {
	Id              int       `db:"id"`
	UserId          int64     `db:"user_id"`
	NabCardGroupsId int       `db:"nab_card_groups_id"`
	RewardType      int       `db:"reward_type"`
	RewardValue     string    `db:"reward_value"`
	CreateTime      time.Time `db:"create_time"`
}

type UserNabexchangeRecordModel interface {
	Insert(ctx context.Context, data *UserNabexchangeRecord) (sql.Result, error)
}

type defaultUserNabexchangeRecordModel struct {
	conn  sqlx.SqlConn
	table string
}

func NewUserNabexchangeRecordModel(conn sqlx.SqlConn) UserNabexchangeRecordModel {
	return &defaultUserNabexchangeRecordModel{
		conn:  conn,
		table: "`db_user_nabexchange_record`",
	}
}

func (m *defaultUserNabexchangeRecordModel) Insert(ctx context.Context, data *UserNabexchangeRecord) (sql.Result, error) {
	query := `insert into ` + m.table + ` (user_id, nab_card_groups_id, reward_type, reward_value) values (?, ?, ?, ?)`
	return m.conn.ExecCtx(ctx, query, data.UserId, data.NabCardGroupsId, data.RewardType, data.RewardValue)
}