package svc

import (
	"github.com/go-redis/redis/v8"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
	"sport-server/internal/config"
	"sport-server/internal/middleware"
	"sport-server/internal/model"
	"sport-server/internal/utils"
)

type ServiceContext struct {
	Config                     config.Config
	Auth                       rest.Middleware
	UserModel                  model.UserModel
	UserGuessRecordModel       model.UserGuessRecordModel
	UserNabGuessRecordModel    model.UserNabGuessRecordModel
	MatchScheduleModel         model.MatchScheduleModel
	MatchScheduleV2Model       model.MatchScheduleV2Model
	FootballMatchModel         model.FootballMatchModel
	SystemConfigModel          model.SystemConfigModel
	NbaCardModel               model.NbaCardModel
	UserNabDateModel           model.UserNabDateModel
	UserNabCardModel           model.UserNabCardModel
	NabCardGroupsModel         model.NabCardGroupsModel
	UserNabexchangeRecordModel model.UserNabexchangeRecordModel
	Redis                      *redis.Client
	conn                       sqlx.SqlConn // 添加数据库连接字段
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 创建数据库连接
	conn := sqlx.NewMysql(c.DataSource)

	// 使用自定义SQL日志包装器
	loggedConn := utils.NewLoggedSqlConn(conn)

	// 创建Redis连接
	rdb := redis.NewClient(&redis.Options{
		Addr:     c.Redis.Host,
		Password: c.Redis.Pass,
		DB:       0,
	})

	return &ServiceContext{
		Config:                     c,
		Auth:                       middleware.NewAuthMiddleware(c).Handle,
		UserModel:                  model.NewUserModel(loggedConn),
		UserGuessRecordModel:       model.NewUserGuessRecordModel(loggedConn),
		UserNabGuessRecordModel:    model.NewUserNabGuessRecordModel(loggedConn),
		MatchScheduleModel:         model.NewMatchScheduleModel(conn),
		MatchScheduleV2Model:       model.NewMatchScheduleV2Model(loggedConn),
		FootballMatchModel:         model.NewFootballMatchModel(loggedConn),
		SystemConfigModel:          model.NewSystemConfigModel(loggedConn),
		NbaCardModel:               model.NewNbaCardModel(loggedConn),
		UserNabDateModel:           model.NewUserNabDateModel(loggedConn),
		UserNabCardModel:           model.NewUserNabCardModel(loggedConn),
		NabCardGroupsModel:         model.NewNabCardGroupsModel(loggedConn),
		UserNabexchangeRecordModel: model.NewUserNabexchangeRecordModel(loggedConn),
		Redis:                      rdb,
		conn:                       loggedConn, // 保存数据库连接
	}
}

// GetConn 获取数据库连接
func (s *ServiceContext) GetConn() sqlx.SqlConn {
	return s.conn
}
