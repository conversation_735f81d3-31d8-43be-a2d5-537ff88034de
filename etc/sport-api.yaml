Name: sport-api
Host: 0.0.0.0
Port: 8888
Timeout: 30000

# 环境配置
Environment: dev

# MySQL配置
DataSource: root:123456@tcp(127.0.0.1:3306)/activity?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

# Redis配置（同时用于缓存）
Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass: "123456"
  Tls: false

# 定时任务配置
Cron:
  Enabled: true

# 日志配置
Log:
  ServiceName: sport-api
  Mode: console
  Level: info
  Encoding: json

# 关闭访问日志
AccessLog: false

# 关闭HTTP访问日志
LogRequest: false
LogResponse: false

# 中间件配置
Middlewares:
  Log: false  # 禁用HTTP日志中间件

# 关闭统计日志
CpuThreshold: 0
MaxConns: 10000
MaxBytes: 1048576
# 关闭指标统计
MetricsUrl: ""
# 关闭负载保护统计
LoadShed:
  CpuThreshold: 0

# JWT配置
Auth:
  AccessSecret: "Nk0IC3bwf5aXDrEckiLkqkTgl"
  AccessExpire: 604800  # 单位：秒