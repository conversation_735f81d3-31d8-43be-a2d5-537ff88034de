syntax = "v1"

info (
	title:    "Sport Guess API"
	produces: "application/json"
	host:     "127.0.0.1:8888"
	schemes:  "http"
)

// UniversalResponse 通用响应类型，用于统一所有接口的返回格式
type UniversalResponse {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	ErrorMsg string      `json:"error_msg"`
	Data     interface{} `json:"data"`
}

// 通用响应结构
type BaseResponse {
	Code     int    `json:"code"`
	Message  string `json:"message"`
	ErrorMsg string `json:"error_msg"`
}

// 第三方登录请求
type ThirdPartyLoginReq {
	UserId     int64  `json:"userId" validate:"required"`
	Name       string `json:"name" validate:"required"`
	PlatformId string `json:"platformId" validate:"required"`
}

type ThirdPartyLoginResp {
	BaseResponse
	Data ThirdPartyLoginData `json:"data"`
}

type ThirdPartyLoginData {
	Url string `json:"url"`
}

type UserDetailReq {}

type UserDetailResp {
	BaseResponse
	Data UserDetailData `json:"data"`
}

type UserDetailData {
	Id              int64  `json:"id"`
	Username        string `json:"username"`
	RewardAmount    string `json:"reward_amount"`
	NbaRewardAmount string `json:"nba_reward_amount"`
}

// 提交竞猜请求
type GuessItem {
	MatchId int `json:"match_id" validate:"required"`
	Type    int `json:"type" validate:"required,oneof=1 2"`
	WhoWin  int `json:"who_win" validate:"required,oneof=1 2"`
}

type SubmitGuessReq {
	Guesses []GuessItem `json:"guesses" validate:"required,min=1"`
}

type SubmitGuessResp {
	BaseResponse
}

// 我的竞猜列表请求
type MyGuessListReq {
	CompShortZh  string `form:"comp_short_zh,optional"`
	RoundNum     int    `form:"round_num,optional" validate:"min=1,max=38"`
	IsOpenResult int    `form:"is_open_result" validate:"required,oneof=1 2"`
}

type MyGuessListResp {
	BaseResponse
}

type MyGuessItem {
	Id               int64   `json:"id"`
	MatchId          int     `json:"match_id"`
	Home             string  `json:"home"`
	Away             string  `json:"away"`
	ConcedeNum       float64 `json:"concede_num"`
	OverUnderNum     float64 `json:"over_under_num"`
	WhoWin           int     `json:"who_win"`
	Result           int     `json:"result"`
	HomeLogo         string  `json:"home_logo"`
	AwayLogo         string  `json:"away_logo"`
	CompShortZh      string  `json:"comp_short_zh"`
	RoundNum         int     `json:"round_num"`
	IsHeavyHammer    int     `json:"is_heavy_hammer"`
	Type             int     `json:"type"`
	IsCanHeavyHammer int     `json:"is_can_heavy_hammer"`
	MatchTimeFormat  string  `json:"match_time_format"`
	HomeGoals        string  `json:"home_goals"`
	AwayGoals        string  `json:"away_goals"`
}

// 设为重锤单请求
type SetHeavyHammerReq {
	UserGuessRecordId int64 `json:"user_guess_record_id" validate:"required"`
}

type SetHeavyHammerResp {
	BaseResponse
}

// 盈利排行榜请求
type ProfitRankingReq {
	StartTime string `form:"start_time,optional"`
	EndTime   string `form:"end_time,optional"`
}

type ProfitRankingResp {
	BaseResponse
}

type ProfitRankingItem {
	Rank         int     `json:"rank"`
	UserName     string  `json:"username"`
	WinCount     int     `json:"win_count"`
	LossCount    int     `json:"loss_count"`
	ProfitAmount float64 `json:"profit_amount"`
}

// 重锤命中率排行榜请求
type HeavyHammerRankingReq {
	StartTime string `form:"start_time,optional"`
	EndTime   string `form:"end_time,optional"`
}

type HeavyHammerRankingResp {
	BaseResponse
	Data []HeavyHammerRankingItem `json:"data"`
}

type HeavyHammerRankingItem {
	Rank       int    `json:"rank"`
	UserName   string `json:"username"`
	HitCount   int    `json:"hit_count"`
	TotalCount int    `json:"total_count"`
	HitRate    string `json:"hit_rate"`
}

// 赛程列表请求
type GetMatchListReq {
	CompShortZh string `form:"comp_short_zh" validate:"required"`
	RoundNum    int    `form:"round_num" validate:"required,min=1,max=38"`
}

type GetMatchListResp {
	BaseResponse
	Data MatchListData `json:"data"`
}

type MatchListData {
	List                   []MatchInfo `json:"list"`
	UserGuessCount         int64       `json:"user_guess_count"`
	MinGuessCountForReward int         `json:"min_guess_count_for_reward"`
	RoundStatus            int         `json:"round_status"`
}

type NbaGetMatchListReq {}

type NbaGetMatchListResp {
	BaseResponse
	Data NbaMatchListData `json:"data"`
}

type NbaMyGuessListReq {
	IsOpenResult int    `form:"is_open_result" validate:"required,oneof=1 2"`
	Date         string `form:"date,optional"`
}

type NbaMyGuessListResp {
	UniversalResponse
}

// NBA提交竞猜请求
type NbaGuessItem {
	MatchId int `json:"match_id" validate:"required"`
	Type    int `json:"type" validate:"required,oneof=1 2"`
	WhoWin  int `json:"who_win" validate:"required,oneof=1 2"`
}

type NbaSubmitGuessReq {
	Guesses []NbaGuessItem `json:"guesses" validate:"required,min=1"`
}

type NbaSubmitGuessResp {
	UniversalResponse
}

// NBA设为重锤单请求
type NbaSetHeavyHammerReq {
	Id int64 `json:"user_guess_record_id" validate:"required"`
}

type NbaSetHeavyHammerResp {
	UniversalResponse
}

// NBA抽卡请求
type NbaDrawCardReq {}

type NbaDrawCardResp {
	UniversalResponse
}

// NBA球星列表请求
type NbaGetStarListReq {
	Level string `form:"level,optional"`
}

type NbaGetStarListResp {
	UniversalResponse
}

// NBA卡组列表请求
type NbaGetCardGroupListReq {}

type NbaGetCardGroupListResp {
	UniversalResponse
}

// NBA卡组兑换请求
type NbaExchangeCardGroupReq {
	NabCardGroupsId int `json:"nab_card_groups_id" validate:"required"`
}

type NbaExchangeCardGroupResp {
	UniversalResponse
}

type NbaMatchListData {
	List           []NbaMatchInfo `json:"list"`
	UserGuessCount int64          `json:"user_guess_count"`
	Date           string         `json:"date"`
}

type DefaultRoundListReq {}

type DefaultRoundListResp {
	BaseResponse
	Data []DefaultRoundItem `json:"data"`
}

type DefaultRoundItem {
	CompShortZh string `json:"comp_short_zh"`
	RoundNum    int    `json:"round_num"`
}

type MatchInfo {
	MatchId        int    `json:"match_id"`
	MatchTime      string `json:"match_time"`
	Home           string `json:"home"`
	HomeLogo       string `json:"home_logo"`
	Away           string `json:"away"`
	AwayLogo       string `json:"away_logo"`
	HomeGoals      *int   `json:"home_goals"`
	AwayGoals      *int   `json:"away_goals"`
	HandicapStatus int    `json:"handicap_status"`
	CloseTime      string `json:"close_time"`
	ConcedeNum     string `json:"concede_num"`
	OverUnderNum   string `json:"over_under_num"`
	HandicapGuess  int    `json:"handicap_guess"`
	OverUnderGuess int    `json:"over_under_guess"`
}

type NbaMatchInfo {
	MatchId        int    `json:"match_id"`
	MatchTime      string `json:"match_time"`
	Home           string `json:"home"`
	HomeLogo       string `json:"home_logo"`
	Away           string `json:"away"`
	AwayLogo       string `json:"away_logo"`
	HomeGoals      *int   `json:"home_goals"`
	AwayGoals      *int   `json:"away_goals"`
	HandicapStatus int    `json:"handicap_status"`
	CloseTime      string `json:"close_time"`
	ConcedeNum     string `json:"concede_num"`
	OverUnderNum   string `json:"over_under_num"`
	HandicapGuess  int    `json:"handicap_guess"`
	OverUnderGuess int    `json:"over_under_guess"`
}

type NbaMyGuessItem {
	Id               int64   `json:"id"`
	MatchId          int     `json:"match_id"`
	Home             string  `json:"home"`
	Away             string  `json:"away"`
	ConcedeNum       float64 `json:"concede_num"`
	OverUnderNum     float64 `json:"over_under_num"`
	WhoWin           int     `json:"who_win"`
	Result           int     `json:"result"`
	HomeLogo         string  `json:"home_logo"`
	AwayLogo         string  `json:"away_logo"`
	IsHeavyHammer    int     `json:"is_heavy_hammer"`
	Type             int     `json:"type"`
	IsCanHeavyHammer int     `json:"is_can_heavy_hammer"`
	MatchTimeFormat  string  `json:"match_time_format"`
	HomeGoals        string  `json:"home_goals"`
	AwayGoals        string  `json:"away_goals"`
}

type GuessResult {
	WhoWin int  `json:"who_win"`
	Result *int `json:"result,omitempty"`
}

@server (
	tags:   "用户"
	prefix: /api/v1
	group:  account
)
service sport-api {
	@doc "第三方登录"
	@handler thirdpartylogin
	post /user/third-party-login (ThirdPartyLoginReq) returns (ThirdPartyLoginResp)
}

@server (
	tags:       "用户"
	prefix:     /api/v1
	group:      account
	middleware: Auth
)
service sport-api {
	@doc "用户信息"
	@handler detail
	post /user/detail (UserDetailReq) returns (UserDetailResp)
}

@server (
	tags:       "赛程"
	prefix:     /api/v1
	group:      match
	middleware: Auth
)
service sport-api {
	@doc "获取赛程列表"
	@handler GetMatchList
	get /match/list (GetMatchListReq) returns (GetMatchListResp)
}

@server (
	tags:   "赛程"
	prefix: /api/v1
	group:  match
)
service sport-api {
	@doc "获取各赛事默认轮次"
	@handler DefaultRoundList
	post /match/default-round-list (DefaultRoundListReq) returns (DefaultRoundListResp)
}

@server (
	tags:   "排行榜"
	prefix: /api/v1
	group:  ranking
)
service sport-api {
	@doc "盈利排行榜"
	@handler ProfitRanking
	get /ranking/profit (ProfitRankingReq) returns (ProfitRankingResp)

	@doc "重锤命中率排行榜"
	@handler HeavyHammerRanking
	get /ranking/heavy-hammer (HeavyHammerRankingReq) returns (HeavyHammerRankingResp)
}

// 需要认证的竞猜接口
@server (
	tags:       "竞猜"
	prefix:     /api/v1
	group:      guess
	middleware: Auth
)
service sport-api {
	@doc "提交竞猜"
	@handler SubmitGuess
	post /guess/submit (SubmitGuessReq) returns (SubmitGuessResp)

	@doc "设为重锤单"
	@handler SetHeavyHammer
	post /guess/set-heavy-hammer (SetHeavyHammerReq) returns (SetHeavyHammerResp)

	@doc "我的竞猜列表"
	@handler MyGuessList
	get /guess/my-list (MyGuessListReq) returns (MyGuessListResp)
}

@server (
	tags:       "NBA活动"
	prefix:     /api/nba
	group:      nab
	middleware: Auth
)
service sport-api {
	@doc "获取NBA赛程列表"
	@handler nabmatchlist
	get /match/list (NbaGetMatchListReq) returns (NbaGetMatchListResp)

	@doc "我的竞猜列表"
	@handler NbaMyGuessList
	get /guess/my-list (NbaMyGuessListReq) returns (NbaMyGuessListResp)

	@doc "提交竞猜"
	@handler NbaSubmitGuess
	post /guess/submit (NbaSubmitGuessReq) returns (NbaSubmitGuessResp)

	@doc "设为重锤单"
	@handler NbaSetHeavyHammer
	post /guess/set-heavy-hammer (NbaSetHeavyHammerReq) returns (NbaSetHeavyHammerResp)

	@doc "NBA抽卡"
	@handler NbaDrawCard
	post /draw-card (NbaDrawCardReq) returns (NbaDrawCardResp)

	@doc "获取NBA球星列表"
	@handler NbaGetStarList
	get /star/list (NbaGetStarListReq) returns (NbaGetStarListResp)

	@doc "获取NBA卡组列表"
	@handler NbaGetCardGroupList
	get /card-group/list (NbaGetCardGroupListReq) returns (NbaGetCardGroupListResp)

	@doc "NBA卡组兑换"
	@handler NbaExchangeCardGroup
	post /card-group/exchange (NbaExchangeCardGroupReq) returns (NbaExchangeCardGroupResp)
}

